var he=Object.defineProperty;var me=(e,t,n)=>t in e?he(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var i=(e,t,n)=>(me(e,typeof t!="symbol"?t+"":t,n),n),ge=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)};var K=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)};var N=(e,t,n)=>(ge(e,t,"access private method"),n);import{r as S,c as m,g as _,h as o}from"./index-6fc59cf8.js";import{a as k,S as x,E as y,c as z,v as j,f as fe,s as te,b as M,u as A,d as Y}from"./sentry-reporter-Di7JtC0A-06833024.js";const be="",xe=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",we=class{constructor(e){i(this,"modalTitle","模拟面试");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请开始模拟面试");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"parsedCustomInputs",{});i(this,"uploadSuccess");i(this,"streamComplete");i(this,"conversationStart");i(this,"interviewComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"interviewMode","text");i(this,"recordingError");i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",null);i(this,"showChatModal",!1);i(this,"jobDescription","");i(this,"isSubmitting",!1);i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"handleClose",()=>{this.modalClosed.emit()});S(this,e),this.modalClosed=m(this,"modalClosed"),this.uploadSuccess=m(this,"uploadSuccess"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent"),this.recordingError=m(this,"recordingError")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}handleCustomInputsChange(){this.parseCustomInputs()}parseCustomInputs(){try{typeof this.customInputs=="string"?this.parsedCustomInputs=JSON.parse(this.customInputs):this.parsedCustomInputs={...this.customInputs}}catch(e){console.error("解析 customInputs 失败:",e),this.parsedCustomInputs={},x.captureError(e,{action:"parseCustomInputs",component:"pcm-1zhanshi-mnms-modal",title:"解析自定义输入参数失败"}),y.emitError({error:e,message:"解析自定义输入参数失败"})}}componentWillLoad(){this.parseCustomInputs(),this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async handleIsOpenChange(e){e?(await j(this.token),this.showChatModal=!0):(this.showChatModal=!1,this.jobDescription="")}render(){var s,a;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal;return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,botId:"3022316191018903",conversationId:this.conversationId,defaultQuery:this.defaultQuery,enableVoice:!1,customInputs:this.conversationId?{}:{...this.parsedCustomInputs,file_url:(s=this.uploadedFileInfo)==null?void 0:s.cos_key,file_name:(a=this.uploadedFileInfo)==null?void 0:a.file_name},interviewMode:this.interviewMode}))))}static get watchers(){return{token:["handleTokenChange"],customInputs:["handleCustomInputsChange"],isOpen:["handleIsOpenChange"]}}};we.style=be+xe;function oe(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let $=oe();function de(e){$=e}const ce=/[&<>"']/,ye=new RegExp(ce.source,"g"),ue=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,ve=new RegExp(ue.source,"g"),ke={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ne=e=>ke[e];function R(e,t){if(t){if(ce.test(e))return e.replace(ye,ne)}else if(ue.test(e))return e.replace(ve,ne);return e}const Ce=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function Ie(e){return e.replace(Ce,(t,n)=>(n=n.toLowerCase(),n==="colon"?":":n.charAt(0)==="#"?n.charAt(1)==="x"?String.fromCharCode(parseInt(n.substring(2),16)):String.fromCharCode(+n.substring(1)):""))}const Fe=/(^|[^\[])\^/g;function I(e,t){e=typeof e=="string"?e:e.source,t=t||"";const n={replace:(r,s)=>(s=typeof s=="object"&&"source"in s?s.source:s,s=s.replace(Fe,"$1"),e=e.replace(r,s),n),getRegex:()=>new RegExp(e,t)};return n}function re(e){try{e=encodeURI(e).replace(/%25/g,"%")}catch{return null}return e}const V={exec:()=>null};function se(e,t){const n=e.replace(/\|/g,(a,l,d)=>{let c=!1,p=l;for(;--p>=0&&d[p]==="\\";)c=!c;return c?"|":" |"}),r=n.split(/ \|/);let s=0;if(r[0].trim()||r.shift(),r.length>0&&!r[r.length-1].trim()&&r.pop(),t)if(r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;s<r.length;s++)r[s]=r[s].trim().replace(/\\\|/g,"|");return r}function Q(e,t,n){const r=e.length;if(r===0)return"";let s=0;for(;s<r&&e.charAt(r-s-1)===t;)s++;return e.slice(0,r-s)}function Ee(e,t){if(e.indexOf(t[1])===-1)return-1;let n=0;for(let r=0;r<e.length;r++)if(e[r]==="\\")r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return-1}function ae(e,t,n,r){const s=t.href,a=t.title?R(t.title):null,l=e[1].replace(/\\([\[\]])/g,"$1");if(e[0].charAt(0)!=="!"){r.state.inLink=!0;const d={type:"link",raw:n,href:s,title:a,text:l,tokens:r.inlineTokens(l)};return r.state.inLink=!1,d}return{type:"image",raw:n,href:s,title:a,text:R(l)}}function Se(e,t){const n=e.match(/^(\s+)(?:```)/);if(n===null)return t;const r=n[1];return t.split(`
`).map(s=>{const a=s.match(/^\s+/);if(a===null)return s;const[l]=a;return l.length>=r.length?s.slice(r.length):s}).join(`
`)}class W{constructor(t){i(this,"options");i(this,"rules");i(this,"lexer");this.options=t||$}space(t){const n=this.rules.block.newline.exec(t);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(t){const n=this.rules.block.code.exec(t);if(n){const r=n[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?r:Q(r,`
`)}}}fences(t){const n=this.rules.block.fences.exec(t);if(n){const r=n[0],s=Se(r,n[3]||"");return{type:"code",raw:r,lang:n[2]?n[2].trim().replace(this.rules.inline._escapes,"$1"):n[2],text:s}}}heading(t){const n=this.rules.block.heading.exec(t);if(n){let r=n[2].trim();if(/#$/.test(r)){const s=Q(r,"#");(this.options.pedantic||!s||/ $/.test(s))&&(r=s.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(t){const n=this.rules.block.hr.exec(t);if(n)return{type:"hr",raw:n[0]}}blockquote(t){const n=this.rules.block.blockquote.exec(t);if(n){const r=Q(n[0].replace(/^ *>[ \t]?/gm,""),`
`),s=this.lexer.state.top;this.lexer.state.top=!0;const a=this.lexer.blockTokens(r);return this.lexer.state.top=s,{type:"blockquote",raw:n[0],tokens:a,text:r}}}list(t){let n=this.rules.block.list.exec(t);if(n){let r=n[1].trim();const s=r.length>1,a={type:"list",raw:"",ordered:s,start:s?+r.slice(0,-1):"",loose:!1,items:[]};r=s?`\\d{1,9}\\${r.slice(-1)}`:`\\${r}`,this.options.pedantic&&(r=s?r:"[*+-]");const l=new RegExp(`^( {0,3}${r})((?:[	 ][^\\n]*)?(?:\\n|$))`);let d="",c="",p=!1;for(;t;){let u=!1;if(!(n=l.exec(t))||this.rules.block.hr.test(t))break;d=n[0],t=t.substring(d.length);let g=n[2].split(`
`,1)[0].replace(/^\t+/,T=>" ".repeat(3*T.length)),h=t.split(`
`,1)[0],f=0;this.options.pedantic?(f=2,c=g.trimStart()):(f=n[2].search(/[^ ]/),f=f>4?1:f,c=g.slice(f),f+=n[1].length);let v=!1;if(!g&&/^ *$/.test(h)&&(d+=h+`
`,t=t.substring(h.length+1),u=!0),!u){const T=new RegExp(`^ {0,${Math.min(3,f-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),D=new RegExp(`^ {0,${Math.min(3,f-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),P=new RegExp(`^ {0,${Math.min(3,f-1)}}(?:\`\`\`|~~~)`),H=new RegExp(`^ {0,${Math.min(3,f-1)}}#`);for(;t;){const G=t.split(`
`,1)[0];if(h=G,this.options.pedantic&&(h=h.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),P.test(h)||H.test(h)||T.test(h)||D.test(t))break;if(h.search(/[^ ]/)>=f||!h.trim())c+=`
`+h.slice(f);else{if(v||g.search(/[^ ]/)>=4||P.test(g)||H.test(g)||D.test(g))break;c+=`
`+h}!v&&!h.trim()&&(v=!0),d+=G+`
`,t=t.substring(G.length+1),g=h.slice(f)}}a.loose||(p?a.loose=!0:/\n *\n *$/.test(d)&&(p=!0));let C=null,E;this.options.gfm&&(C=/^\[[ xX]\] /.exec(c),C&&(E=C[0]!=="[ ] ",c=c.replace(/^\[[ xX]\] +/,""))),a.items.push({type:"list_item",raw:d,task:!!C,checked:E,loose:!1,text:c,tokens:[]}),a.raw+=d}a.items[a.items.length-1].raw=d.trimEnd(),a.items[a.items.length-1].text=c.trimEnd(),a.raw=a.raw.trimEnd();for(let u=0;u<a.items.length;u++)if(this.lexer.state.top=!1,a.items[u].tokens=this.lexer.blockTokens(a.items[u].text,[]),!a.loose){const g=a.items[u].tokens.filter(f=>f.type==="space"),h=g.length>0&&g.some(f=>/\n.*\n/.test(f.raw));a.loose=h}if(a.loose)for(let u=0;u<a.items.length;u++)a.items[u].loose=!0;return a}}html(t){const n=this.rules.block.html.exec(t);if(n)return{type:"html",block:!0,raw:n[0],pre:n[1]==="pre"||n[1]==="script"||n[1]==="style",text:n[0]}}def(t){const n=this.rules.block.def.exec(t);if(n){const r=n[1].toLowerCase().replace(/\s+/g," "),s=n[2]?n[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",a=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline._escapes,"$1"):n[3];return{type:"def",tag:r,raw:n[0],href:s,title:a}}}table(t){const n=this.rules.block.table.exec(t);if(n){if(!/[:|]/.test(n[2]))return;const r={type:"table",raw:n[0],header:se(n[1]).map(s=>({text:s,tokens:[]})),align:n[2].replace(/^\||\| *$/g,"").split("|"),rows:n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split(`
`):[]};if(r.header.length===r.align.length){let s=r.align.length,a,l,d,c;for(a=0;a<s;a++){const p=r.align[a];p&&(/^ *-+: *$/.test(p)?r.align[a]="right":/^ *:-+: *$/.test(p)?r.align[a]="center":/^ *:-+ *$/.test(p)?r.align[a]="left":r.align[a]=null)}for(s=r.rows.length,a=0;a<s;a++)r.rows[a]=se(r.rows[a],r.header.length).map(p=>({text:p,tokens:[]}));for(s=r.header.length,l=0;l<s;l++)r.header[l].tokens=this.lexer.inline(r.header[l].text);for(s=r.rows.length,l=0;l<s;l++)for(c=r.rows[l],d=0;d<c.length;d++)c[d].tokens=this.lexer.inline(c[d].text);return r}}}lheading(t){const n=this.rules.block.lheading.exec(t);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(t){const n=this.rules.block.paragraph.exec(t);if(n){const r=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:r,tokens:this.lexer.inline(r)}}}text(t){const n=this.rules.block.text.exec(t);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(t){const n=this.rules.inline.escape.exec(t);if(n)return{type:"escape",raw:n[0],text:R(n[1])}}tag(t){const n=this.rules.inline.tag.exec(t);if(n)return!this.lexer.state.inLink&&/^<a /i.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:n[0]}}link(t){const n=this.rules.inline.link.exec(t);if(n){const r=n[2].trim();if(!this.options.pedantic&&/^</.test(r)){if(!/>$/.test(r))return;const l=Q(r.slice(0,-1),"\\");if((r.length-l.length)%2===0)return}else{const l=Ee(n[2],"()");if(l>-1){const c=(n[0].indexOf("!")===0?5:4)+n[1].length+l;n[2]=n[2].substring(0,l),n[0]=n[0].substring(0,c).trim(),n[3]=""}}let s=n[2],a="";if(this.options.pedantic){const l=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(s);l&&(s=l[1],a=l[3])}else a=n[3]?n[3].slice(1,-1):"";return s=s.trim(),/^</.test(s)&&(this.options.pedantic&&!/>$/.test(r)?s=s.slice(1):s=s.slice(1,-1)),ae(n,{href:s&&s.replace(this.rules.inline._escapes,"$1"),title:a&&a.replace(this.rules.inline._escapes,"$1")},n[0],this.lexer)}}reflink(t,n){let r;if((r=this.rules.inline.reflink.exec(t))||(r=this.rules.inline.nolink.exec(t))){let s=(r[2]||r[1]).replace(/\s+/g," ");if(s=n[s.toLowerCase()],!s){const a=r[0].charAt(0);return{type:"text",raw:a,text:a}}return ae(r,s,r[0],this.lexer)}}emStrong(t,n,r=""){let s=this.rules.inline.emStrong.lDelim.exec(t);if(!s||s[3]&&r.match(/[\p{L}\p{N}]/u))return;if(!(s[1]||s[2]||"")||!r||this.rules.inline.punctuation.exec(r)){const l=[...s[0]].length-1;let d,c,p=l,u=0;const g=s[0][0]==="*"?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(g.lastIndex=0,n=n.slice(-1*t.length+l);(s=g.exec(n))!=null;){if(d=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!d)continue;if(c=[...d].length,s[3]||s[4]){p+=c;continue}else if((s[5]||s[6])&&l%3&&!((l+c)%3)){u+=c;continue}if(p-=c,p>0)continue;c=Math.min(c,c+p+u);const h=[...s[0]][0].length,f=t.slice(0,l+s.index+h+c);if(Math.min(l,c)%2){const C=f.slice(1,-1);return{type:"em",raw:f,text:C,tokens:this.lexer.inlineTokens(C)}}const v=f.slice(2,-2);return{type:"strong",raw:f,text:v,tokens:this.lexer.inlineTokens(v)}}}}codespan(t){const n=this.rules.inline.code.exec(t);if(n){let r=n[2].replace(/\n/g," ");const s=/[^ ]/.test(r),a=/^ /.test(r)&&/ $/.test(r);return s&&a&&(r=r.substring(1,r.length-1)),r=R(r,!0),{type:"codespan",raw:n[0],text:r}}}br(t){const n=this.rules.inline.br.exec(t);if(n)return{type:"br",raw:n[0]}}del(t){const n=this.rules.inline.del.exec(t);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(t){const n=this.rules.inline.autolink.exec(t);if(n){let r,s;return n[2]==="@"?(r=R(n[1]),s="mailto:"+r):(r=R(n[1]),s=r),{type:"link",raw:n[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}url(t){let n;if(n=this.rules.inline.url.exec(t)){let r,s;if(n[2]==="@")r=R(n[0]),s="mailto:"+r;else{let a;do a=n[0],n[0]=this.rules.inline._backpedal.exec(n[0])[0];while(a!==n[0]);r=R(n[0]),n[1]==="www."?s="http://"+n[0]:s=n[0]}return{type:"link",raw:n[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(t){const n=this.rules.inline.text.exec(t);if(n){let r;return this.lexer.state.inRawBlock?r=n[0]:r=R(n[0]),{type:"text",raw:n[0],text:r}}}}const w={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:V,lheading:/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/};w._label=/(?!\s*\])(?:\\.|[^\[\]\\])+/;w._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/;w.def=I(w.def).replace("label",w._label).replace("title",w._title).getRegex();w.bullet=/(?:[*+-]|\d{1,9}[.)])/;w.listItemStart=I(/^( *)(bull) */).replace("bull",w.bullet).getRegex();w.list=I(w.list).replace(/bull/g,w.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+w.def.source+")").getRegex();w._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul";w._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/;w.html=I(w.html,"i").replace("comment",w._comment).replace("tag",w._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();w.lheading=I(w.lheading).replace(/bull/g,w.bullet).getRegex();w.paragraph=I(w._paragraph).replace("hr",w.hr).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",w._tag).getRegex();w.blockquote=I(w.blockquote).replace("paragraph",w.paragraph).getRegex();w.normal={...w};w.gfm={...w.normal,table:"^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"};w.gfm.table=I(w.gfm.table).replace("hr",w.hr).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",w._tag).getRegex();w.gfm.paragraph=I(w._paragraph).replace("hr",w.hr).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",w.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",w._tag).getRegex();w.pedantic={...w.normal,html:I(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",w._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:V,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:I(w.normal._paragraph).replace("hr",w.hr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",w.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};const b={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:V,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,rDelimAst:/^[^_*]*?__[^_*]*?\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\*)[punct](\*+)(?=[\s]|$)|[^punct\s](\*+)(?!\*)(?=[punct\s]|$)|(?!\*)[punct\s](\*+)(?=[^punct\s])|[\s](\*+)(?!\*)(?=[punct])|(?!\*)[punct](\*+)(?!\*)(?=[punct])|[^punct\s](\*+)(?=[^punct\s])/,rDelimUnd:/^[^_*]*?\*\*[^_*]*?_[^_*]*?(?=\*\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\s]|$)|[^punct\s](_+)(?!_)(?=[punct\s]|$)|(?!_)[punct\s](_+)(?=[^punct\s])|[\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:V,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^((?![*_])[\spunctuation])/};b._punctuation="\\p{P}$+<=>`^|~";b.punctuation=I(b.punctuation,"u").replace(/punctuation/g,b._punctuation).getRegex();b.blockSkip=/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g;b.anyPunctuation=/\\[punct]/g;b._escapes=/\\([punct])/g;b._comment=I(w._comment).replace("(?:-->|$)","-->").getRegex();b.emStrong.lDelim=I(b.emStrong.lDelim,"u").replace(/punct/g,b._punctuation).getRegex();b.emStrong.rDelimAst=I(b.emStrong.rDelimAst,"gu").replace(/punct/g,b._punctuation).getRegex();b.emStrong.rDelimUnd=I(b.emStrong.rDelimUnd,"gu").replace(/punct/g,b._punctuation).getRegex();b.anyPunctuation=I(b.anyPunctuation,"gu").replace(/punct/g,b._punctuation).getRegex();b._escapes=I(b._escapes,"gu").replace(/punct/g,b._punctuation).getRegex();b._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;b._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;b.autolink=I(b.autolink).replace("scheme",b._scheme).replace("email",b._email).getRegex();b._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/;b.tag=I(b.tag).replace("comment",b._comment).replace("attribute",b._attribute).getRegex();b._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;b._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/;b._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/;b.link=I(b.link).replace("label",b._label).replace("href",b._href).replace("title",b._title).getRegex();b.reflink=I(b.reflink).replace("label",b._label).replace("ref",w._label).getRegex();b.nolink=I(b.nolink).replace("ref",w._label).getRegex();b.reflinkSearch=I(b.reflinkSearch,"g").replace("reflink",b.reflink).replace("nolink",b.nolink).getRegex();b.normal={...b};b.pedantic={...b.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:I(/^!?\[(label)\]\((.*?)\)/).replace("label",b._label).getRegex(),reflink:I(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",b._label).getRegex()};b.gfm={...b.normal,escape:I(b.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/};b.gfm.url=I(b.gfm.url,"i").replace("email",b.gfm._extended_email).getRegex();b.breaks={...b.gfm,br:I(b.br).replace("{2,}","*").getRegex(),text:I(b.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};class B{constructor(t){i(this,"tokens");i(this,"options");i(this,"state");i(this,"tokenizer");i(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||$,this.options.tokenizer=this.options.tokenizer||new W,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={block:w.normal,inline:b.normal};this.options.pedantic?(n.block=w.pedantic,n.inline=b.pedantic):this.options.gfm&&(n.block=w.gfm,this.options.breaks?n.inline=b.breaks:n.inline=b.gfm),this.tokenizer.rules=n}static get rules(){return{block:w,inline:b}}static lex(t,n){return new B(n).lex(t)}static lexInline(t,n){return new B(n).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);let n;for(;n=this.inlineQueue.shift();)this.inlineTokens(n.src,n.tokens);return this.tokens}blockTokens(t,n=[]){this.options.pedantic?t=t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t=t.replace(/^( *)(\t+)/gm,(d,c,p)=>c+"    ".repeat(p.length));let r,s,a,l;for(;t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(d=>(r=d.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.space(t)){t=t.substring(r.raw.length),r.raw.length===1&&n.length>0?n[n.length-1].raw+=`
`:n.push(r);continue}if(r=this.tokenizer.code(t)){t=t.substring(r.raw.length),s=n[n.length-1],s&&(s.type==="paragraph"||s.type==="text")?(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text):n.push(r);continue}if(r=this.tokenizer.fences(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.heading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.hr(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.blockquote(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.list(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.html(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.def(t)){t=t.substring(r.raw.length),s=n[n.length-1],s&&(s.type==="paragraph"||s.type==="text")?(s.raw+=`
`+r.raw,s.text+=`
`+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.lheading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(a=t,this.options.extensions&&this.options.extensions.startBlock){let d=1/0;const c=t.slice(1);let p;this.options.extensions.startBlock.forEach(u=>{p=u.call({lexer:this},c),typeof p=="number"&&p>=0&&(d=Math.min(d,p))}),d<1/0&&d>=0&&(a=t.substring(0,d+1))}if(this.state.top&&(r=this.tokenizer.paragraph(a))){s=n[n.length-1],l&&s.type==="paragraph"?(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):n.push(r),l=a.length!==t.length,t=t.substring(r.raw.length);continue}if(r=this.tokenizer.text(t)){t=t.substring(r.raw.length),s=n[n.length-1],s&&s.type==="text"?(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):n.push(r);continue}if(t){const d="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(d);break}else throw new Error(d)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){let r,s,a,l=t,d,c,p;if(this.tokens.links){const u=Object.keys(this.tokens.links);if(u.length>0)for(;(d=this.tokenizer.rules.inline.reflinkSearch.exec(l))!=null;)u.includes(d[0].slice(d[0].lastIndexOf("[")+1,-1))&&(l=l.slice(0,d.index)+"["+"a".repeat(d[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(d=this.tokenizer.rules.inline.blockSkip.exec(l))!=null;)l=l.slice(0,d.index)+"["+"a".repeat(d[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(d=this.tokenizer.rules.inline.anyPunctuation.exec(l))!=null;)l=l.slice(0,d.index)+"++"+l.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(c||(p=""),c=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(u=>(r=u.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.escape(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.tag(t)){t=t.substring(r.raw.length),s=n[n.length-1],s&&r.type==="text"&&s.type==="text"?(s.raw+=r.raw,s.text+=r.text):n.push(r);continue}if(r=this.tokenizer.link(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(r.raw.length),s=n[n.length-1],s&&r.type==="text"&&s.type==="text"?(s.raw+=r.raw,s.text+=r.text):n.push(r);continue}if(r=this.tokenizer.emStrong(t,l,p)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.codespan(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.br(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.del(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.autolink(t)){t=t.substring(r.raw.length),n.push(r);continue}if(!this.state.inLink&&(r=this.tokenizer.url(t))){t=t.substring(r.raw.length),n.push(r);continue}if(a=t,this.options.extensions&&this.options.extensions.startInline){let u=1/0;const g=t.slice(1);let h;this.options.extensions.startInline.forEach(f=>{h=f.call({lexer:this},g),typeof h=="number"&&h>=0&&(u=Math.min(u,h))}),u<1/0&&u>=0&&(a=t.substring(0,u+1))}if(r=this.tokenizer.inlineText(a)){t=t.substring(r.raw.length),r.raw.slice(-1)!=="_"&&(p=r.raw.slice(-1)),c=!0,s=n[n.length-1],s&&s.type==="text"?(s.raw+=r.raw,s.text+=r.text):n.push(r);continue}if(t){const u="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(u);break}else throw new Error(u)}}return n}}class J{constructor(t){i(this,"options");this.options=t||$}code(t,n,r){var a;const s=(a=(n||"").match(/^\S*/))==null?void 0:a[0];return t=t.replace(/\n$/,"")+`
`,s?'<pre><code class="language-'+R(s)+'">'+(r?t:R(t,!0))+`</code></pre>
`:"<pre><code>"+(r?t:R(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,n){return t}heading(t,n,r){return`<h${n}>${t}</h${n}>
`}hr(){return`<hr>
`}list(t,n,r){const s=n?"ol":"ul",a=n&&r!==1?' start="'+r+'"':"";return"<"+s+a+`>
`+t+"</"+s+`>
`}listitem(t,n,r){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,n){return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+n+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,n){const r=n.header?"th":"td";return(n.align?`<${r} align="${n.align}">`:`<${r}>`)+t+`</${r}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,n,r){const s=re(t);if(s===null)return r;t=s;let a='<a href="'+t+'"';return n&&(a+=' title="'+n+'"'),a+=">"+r+"</a>",a}image(t,n,r){const s=re(t);if(s===null)return r;t=s;let a=`<img src="${t}" alt="${r}"`;return n&&(a+=` title="${n}"`),a+=">",a}text(t){return t}}class ie{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,n,r){return""+r}image(t,n,r){return""+r}br(){return""}}class L{constructor(t){i(this,"options");i(this,"renderer");i(this,"textRenderer");this.options=t||$,this.options.renderer=this.options.renderer||new J,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new ie}static parse(t,n){return new L(n).parse(t)}static parseInline(t,n){return new L(n).parseInline(t)}parse(t,n=!0){let r="";for(let s=0;s<t.length;s++){const a=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]){const l=a,d=this.options.extensions.renderers[l.type].call({parser:this},l);if(d!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){r+=d||"";continue}}switch(a.type){case"space":continue;case"hr":{r+=this.renderer.hr();continue}case"heading":{const l=a;r+=this.renderer.heading(this.parseInline(l.tokens),l.depth,Ie(this.parseInline(l.tokens,this.textRenderer)));continue}case"code":{const l=a;r+=this.renderer.code(l.text,l.lang,!!l.escaped);continue}case"table":{const l=a;let d="",c="";for(let u=0;u<l.header.length;u++)c+=this.renderer.tablecell(this.parseInline(l.header[u].tokens),{header:!0,align:l.align[u]});d+=this.renderer.tablerow(c);let p="";for(let u=0;u<l.rows.length;u++){const g=l.rows[u];c="";for(let h=0;h<g.length;h++)c+=this.renderer.tablecell(this.parseInline(g[h].tokens),{header:!1,align:l.align[h]});p+=this.renderer.tablerow(c)}r+=this.renderer.table(d,p);continue}case"blockquote":{const l=a,d=this.parse(l.tokens);r+=this.renderer.blockquote(d);continue}case"list":{const l=a,d=l.ordered,c=l.start,p=l.loose;let u="";for(let g=0;g<l.items.length;g++){const h=l.items[g],f=h.checked,v=h.task;let C="";if(h.task){const E=this.renderer.checkbox(!!f);p?h.tokens.length>0&&h.tokens[0].type==="paragraph"?(h.tokens[0].text=E+" "+h.tokens[0].text,h.tokens[0].tokens&&h.tokens[0].tokens.length>0&&h.tokens[0].tokens[0].type==="text"&&(h.tokens[0].tokens[0].text=E+" "+h.tokens[0].tokens[0].text)):h.tokens.unshift({type:"text",text:E+" "}):C+=E+" "}C+=this.parse(h.tokens,p),u+=this.renderer.listitem(C,v,!!f)}r+=this.renderer.list(u,d,c);continue}case"html":{const l=a;r+=this.renderer.html(l.text,l.block);continue}case"paragraph":{const l=a;r+=this.renderer.paragraph(this.parseInline(l.tokens));continue}case"text":{let l=a,d=l.tokens?this.parseInline(l.tokens):l.text;for(;s+1<t.length&&t[s+1].type==="text";)l=t[++s],d+=`
`+(l.tokens?this.parseInline(l.tokens):l.text);r+=n?this.renderer.paragraph(d):d;continue}default:{const l='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return r}parseInline(t,n){n=n||this.renderer;let r="";for(let s=0;s<t.length;s++){const a=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]){const l=this.options.extensions.renderers[a.type].call({parser:this},a);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type)){r+=l||"";continue}}switch(a.type){case"escape":{const l=a;r+=n.text(l.text);break}case"html":{const l=a;r+=n.html(l.text);break}case"link":{const l=a;r+=n.link(l.href,l.title,this.parseInline(l.tokens,n));break}case"image":{const l=a;r+=n.image(l.href,l.title,l.text);break}case"strong":{const l=a;r+=n.strong(this.parseInline(l.tokens,n));break}case"em":{const l=a;r+=n.em(this.parseInline(l.tokens,n));break}case"codespan":{const l=a;r+=n.codespan(l.text);break}case"br":{r+=n.br();break}case"del":{const l=a;r+=n.del(this.parseInline(l.tokens,n));break}case"text":{const l=a;r+=n.text(l.text);break}default:{const l='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return r}}class q{constructor(t){i(this,"options");this.options=t||$}preprocess(t){return t}postprocess(t){return t}}i(q,"passThroughHooks",new Set(["preprocess","postprocess"]));var O,ee,Z,pe;class Te{constructor(...t){K(this,O);K(this,Z);i(this,"defaults",oe());i(this,"options",this.setOptions);i(this,"parse",N(this,O,ee).call(this,B.lex,L.parse));i(this,"parseInline",N(this,O,ee).call(this,B.lexInline,L.parseInline));i(this,"Parser",L);i(this,"Renderer",J);i(this,"TextRenderer",ie);i(this,"Lexer",B);i(this,"Tokenizer",W);i(this,"Hooks",q);this.use(...t)}walkTokens(t,n){var s,a;let r=[];for(const l of t)switch(r=r.concat(n.call(this,l)),l.type){case"table":{const d=l;for(const c of d.header)r=r.concat(this.walkTokens(c.tokens,n));for(const c of d.rows)for(const p of c)r=r.concat(this.walkTokens(p.tokens,n));break}case"list":{const d=l;r=r.concat(this.walkTokens(d.items,n));break}default:{const d=l;(a=(s=this.defaults.extensions)==null?void 0:s.childTokens)!=null&&a[d.type]?this.defaults.extensions.childTokens[d.type].forEach(c=>{r=r.concat(this.walkTokens(d[c],n))}):d.tokens&&(r=r.concat(this.walkTokens(d.tokens,n)))}}return r}use(...t){const n=this.defaults.extensions||{renderers:{},childTokens:{}};return t.forEach(r=>{const s={...r};if(s.async=this.defaults.async||s.async||!1,r.extensions&&(r.extensions.forEach(a=>{if(!a.name)throw new Error("extension name required");if("renderer"in a){const l=n.renderers[a.name];l?n.renderers[a.name]=function(...d){let c=a.renderer.apply(this,d);return c===!1&&(c=l.apply(this,d)),c}:n.renderers[a.name]=a.renderer}if("tokenizer"in a){if(!a.level||a.level!=="block"&&a.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const l=n[a.level];l?l.unshift(a.tokenizer):n[a.level]=[a.tokenizer],a.start&&(a.level==="block"?n.startBlock?n.startBlock.push(a.start):n.startBlock=[a.start]:a.level==="inline"&&(n.startInline?n.startInline.push(a.start):n.startInline=[a.start]))}"childTokens"in a&&a.childTokens&&(n.childTokens[a.name]=a.childTokens)}),s.extensions=n),r.renderer){const a=this.defaults.renderer||new J(this.defaults);for(const l in r.renderer){const d=r.renderer[l],c=l,p=a[c];a[c]=(...u)=>{let g=d.apply(a,u);return g===!1&&(g=p.apply(a,u)),g||""}}s.renderer=a}if(r.tokenizer){const a=this.defaults.tokenizer||new W(this.defaults);for(const l in r.tokenizer){const d=r.tokenizer[l],c=l,p=a[c];a[c]=(...u)=>{let g=d.apply(a,u);return g===!1&&(g=p.apply(a,u)),g}}s.tokenizer=a}if(r.hooks){const a=this.defaults.hooks||new q;for(const l in r.hooks){const d=r.hooks[l],c=l,p=a[c];q.passThroughHooks.has(l)?a[c]=u=>{if(this.defaults.async)return Promise.resolve(d.call(a,u)).then(h=>p.call(a,h));const g=d.call(a,u);return p.call(a,g)}:a[c]=(...u)=>{let g=d.apply(a,u);return g===!1&&(g=p.apply(a,u)),g}}s.hooks=a}if(r.walkTokens){const a=this.defaults.walkTokens,l=r.walkTokens;s.walkTokens=function(d){let c=[];return c.push(l.call(this,d)),a&&(c=c.concat(a.call(this,d))),c}}this.defaults={...this.defaults,...s}}),this}setOptions(t){return this.defaults={...this.defaults,...t},this}lexer(t,n){return B.lex(t,n??this.defaults)}parser(t,n){return L.parse(t,n??this.defaults)}}O=new WeakSet,ee=function(t,n){return(r,s)=>{const a={...s},l={...this.defaults,...a};this.defaults.async===!0&&a.async===!1&&(l.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),l.async=!0);const d=N(this,Z,pe).call(this,!!l.silent,!!l.async);if(typeof r>"u"||r===null)return d(new Error("marked(): input parameter is undefined or null"));if(typeof r!="string")return d(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(r)+", string expected"));if(l.hooks&&(l.hooks.options=l),l.async)return Promise.resolve(l.hooks?l.hooks.preprocess(r):r).then(c=>t(c,l)).then(c=>l.walkTokens?Promise.all(this.walkTokens(c,l.walkTokens)).then(()=>c):c).then(c=>n(c,l)).then(c=>l.hooks?l.hooks.postprocess(c):c).catch(d);try{l.hooks&&(r=l.hooks.preprocess(r));const c=t(r,l);l.walkTokens&&this.walkTokens(c,l.walkTokens);let p=n(c,l);return l.hooks&&(p=l.hooks.postprocess(p)),p}catch(c){return d(c)}}},Z=new WeakSet,pe=function(t,n){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,t){const s="<p>An error occurred:</p><pre>"+R(r.message+"",!0)+"</pre>";return n?Promise.resolve(s):s}if(n)return Promise.reject(r);throw r}};const U=new Te;function F(e,t){return U.parse(e,t)}F.options=F.setOptions=function(e){return U.setOptions(e),F.defaults=U.defaults,de(F.defaults),F};F.getDefaults=oe;F.defaults=$;F.use=function(...e){return U.use(...e),F.defaults=U.defaults,de(F.defaults),F};F.walkTokens=function(e,t){return U.walkTokens(e,t)};F.parseInline=U.parseInline;F.Parser=L;F.parser=L.parse;F.Renderer=J;F.TextRenderer=ie;F.Lexer=B;F.lexer=B.lex;F.Tokenizer=W;F.Hooks=q;F.parse=F;const ze=":host{display:block;font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;overflow-y:auto;padding:20px;z-index:1000}.fullscreen-overlay{padding:0}.modal-container{background:white;border-radius:8px;width:100%;max-width:900px;display:flex;flex-direction:column;position:relative;margin:auto}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.modal-container.fullscreen>div:not(.modal-header):not(.initial-upload){display:flex;flex-direction:column;flex:1;overflow:hidden;height:100%}.pc-layout{width:80%;max-width:800px;min-width:320px;min-height:400px}.video-preview.placeholder{display:flex;justify-content:center;align-items:center;background:#EAEAEA}.placeholder-status{color:#00000066}.placeholder-status p{font-size:16px}.waiting-message p{margin:0;font-size:16px;color:white;font-weight:500}.recording-container{width:100%;display:flex;flex-direction:column;align-items:center}.video-container{width:100%;display:flex;flex-wrap:wrap;justify-content:center;margin-bottom:20px}.video-area{width:100%;display:flex;flex-direction:column;align-items:center}.stop-recording-button{width:100%;height:100%;font-size:16px;background:#f44336;border-radius:6px;color:white;border:none;cursor:pointer}.stop-recording-button:hover{background:#d32f2f}.play-audio-container{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0;}.header-left{display:flex;align-items:center;gap:8px}.header-left div{font-size:16px}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.chat-container{background-image:url(https://pub.pincaimao.com/static/web/images/login/bg_login_m.png);background-size:100%;height:100%;border-radius:0px 0px 8px 8px}.chat-history{position:relative;flex:1;overflow-y:auto;padding:20px;scroll-behavior:smooth;height:400px}.fullscreen .chat-history{height:auto;flex:1 1 auto}.message-input{padding:16px;border-top:1px solid #eee;display:flex;gap:8px;align-items:center}.message-input input{flex:1;padding:8px 12px;border:1px solid #ddd;border-radius:4px;outline:none;transition:border-color 0.2s ease}.message-input input:focus{border-color:#bbb}.message{margin-bottom:16px;opacity:1;transition:opacity 0.3s ease}.message-content{max-width:70%;padding:8px 12px;border-radius:8px;word-break:break-word}.message-content p{margin:0;word-break:break-word}.user-message{display:flex;justify-content:flex-end}.agent-message{display:flex;justify-content:flex-start}.user-message .message-content{background-color:#007bff;color:white}.agent-message .message-content{background-color:#f1f1f1}.message-time{font-size:12px;color:#999;margin-top:4px;display:block}.send-button{width:38px;height:38px;border-radius:16px;background:#0d75fb;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:background-color 0.2s ease}.send-button img{width:24px;height:24px}.send-button:hover{background:#0a62d6}.send-button.disabled{background:#d9d9d9;cursor:not-allowed}.empty-state{display:flex;justify-content:center;align-items:center;height:100%;color:#999;text-align:center}.loading-container{position:absolute;top:0;left:0;right:0;bottom:0;display:flex;flex-direction:column;justify-content:center;align-items:center;background-color:rgba(255, 255, 255, 0.98);z-index:1;opacity:1;transition:opacity 0.3s ease}.loading-container p{margin-top:16px;color:#666;font-size:14px}.loading-spinner{width:40px;height:40px;border:3px solid #f3f3f3;border-top:3px solid #1890ff;border-radius:50%;animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.messages-wrapper{width:100%;min-height:100%;display:flex;flex-direction:column;justify-content:flex-end}.messages-wrapper.has-overflow{justify-content:flex-start}.suggested-questions{display:flex;flex-direction:column;gap:8px;padding:16px}.suggested-question{display:flex;align-items:center;justify-content:space-between;padding:8px 12px;background-color:#f3f4f6;border-radius:4px;cursor:pointer;font-size:14px;color:#374151;transition:background-color 0.2s}.suggested-question:hover{background-color:#e5e7eb}.arrow-right{margin-left:8px}.loading-suggestions{display:flex;justify-content:center;padding:16px}.loading-spinner-small{width:20px;height:20px;border:2px solid #e5e7eb;border-top-color:#6b7280;border-radius:50%;animation:spin 1s linear infinite}.upload-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;color:#666;border-radius:4px;transition:background-color 0.2s}.upload-button:hover{background-color:rgba(0, 0, 0, 0.04)}.upload-button svg{width:20px;height:20px}.file-input{display:none}.selected-file{font-size:12px;color:#666;margin-left:8px;max-width:150px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.input-wrapper{flex:1;display:flex;align-items:center;border:1px solid #ddd;border-radius:4px;padding:0 4px;background:white}.input-wrapper input{border:none;flex:1;padding:8px;outline:none}.input-wrapper:focus-within{border-color:#bbb}.file-preview{padding:8px 16px;border-top:1px solid #eee;background-color:#f9f9f9}.recording-section{border-top:1px solid #eee;display:flex;flex-direction:column;align-items:center;padding:10px 20px 0px 20px;border-radius:14px 14px 0 0;flex:0 0 auto}.recording-section .video-preview{width:100%;height:200px;max-width:400px;position:relative;margin-bottom:10px;border:1px solid #ddd;border-radius:12px;overflow:hidden}.recording-section video{width:100%;height:100%;object-fit:cover}.recording-status{position:absolute;top:10px;left:10px;background-color:rgba(0, 0, 0, 0.6);color:white;padding:4px 8px;border-radius:4px;display:flex;align-items:center;gap:5px;font-size:14px;z-index:2}.recording-status .recording-dot{display:inline-block;width:10px;height:10px;background-color:red;border-radius:50%;margin-right:5px;animation:blink 1s infinite}.recording-status.warning{color:#ff4d4f;animation:blink 1s infinite}@keyframes blink{0%{opacity:1}50%{opacity:0.5}100%{opacity:1}}.recording-section .stop-recording-button{background-color:#f44336;color:white;border:none;cursor:pointer;font-weight:bold}.recording-section .stop-recording-button:hover{background-color:#d32f2f}.fullscreen{width:100vw;border-radius:0;height:100vh;display:flex;flex-direction:column;overflow-y:auto}.recording-controls{margin-top:10px;height:53px;width:100%;max-width:400px;display:flex;justify-content:center}.recording-controls .waiting-message{text-align:center;color:white;font-size:16px;background-color:#0D75FB;border-radius:6px;width:95%;display:flex;justify-content:center;align-items:center;cursor:pointer}.recording-controls .waiting-message.loading{background:#faad14}.recording-controls .waiting-message p{margin:0;font-size:16px;color:white;font-weight:500}.recording-controls .stop-recording-button{background-color:#dc3545;color:white;border:none;cursor:pointer;font-size:16px}.recording-controls .stop-recording-button:hover{background-color:#c82333}.recording-controls .stop-recording-button.disabled{background:#ccc;cursor:not-allowed}.recording-controls .stop-recording-button.disabled:hover{background:#ccc}.progress-container{display:flex;justify-content:space-between;align-items:center;width:100%;max-width:400px;margin-top:10px;padding:0 5px}.progress-bar-container{height:4px;background-color:#E5E5E5;border-radius:2px;overflow:hidden;margin-right:10px;width:75px}.progress-bar{height:100%;background-image:linear-gradient(111deg, #4A9FFF 0%, #1058FF 100%);border-radius:2px;transition:width 0.3s ease}.progress-text{font-size:14px;color:#666;white-space:nowrap}.text-input-area{display:flex;flex-direction:column;width:100%;height:100%;padding:16px;border-radius:8px;border:none;}.text-answer-input{flex:1;min-height:80px;padding:12px 12px 0px 12px;border:1px solid #ddd;border-radius:8px 8px 0 0;resize:none;font-size:16px;background-color:#fff;border-bottom:none;outline:none;}.input-toolbar{display:flex;justify-content:end;align-items:center;padding:8px 12px;background-color:#fff;border:1px solid #ddd;border-top:none;border-radius:0 0 8px 8px}.text-answer-input:focus{border-color:rgb(74, 144, 226);border-bottom:none}.text-answer-input:focus+.input-toolbar{border-color:rgb(74, 144, 226);border-top:none}.toolbar-actions{width:32px;height:32px;display:flex;justify-content:center;align-items:center;margin-right:10px;border:1px solid #d9d9d9;border-radius:6px}.toolbar-actions:hover{background-color:#f0f0f0}.toolbar-button{background:transparent;border:none;color:#666;cursor:pointer;padding:0;margin:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center}.toolbar-button>div,.toolbar-button>svg{display:flex;justify-content:center;align-items:center}.submit-text-button{padding:6px 16px;background-color:#4a90e2;color:white;border:none;border-radius:4px;font-size:14px;font-weight:500;cursor:pointer;transition:background-color 0.2s}.submit-text-button:hover:not(.disabled){background-color:#3a7bc8}.submit-text-button.disabled{background-color:#b3b3b3;cursor:not-allowed}.toolbar-button.recording{background-color:rgba(255, 0, 0, 0.1);color:#ff3b30;animation:pulse 1.5s infinite}.toolbar-button.converting{background-color:rgba(0, 122, 255, 0.1);color:#007aff}.toolbar-button .recording-time{font-size:12px;margin-left:4px}.converting-indicator{display:flex;justify-content:center;align-items:center}.converting-indicator svg{animation:spin 1.5s linear infinite}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes pulse{0%{box-shadow:0 0 0 0 rgba(255, 0, 0, 0.4)}70%{box-shadow:0 0 0 6px rgba(255, 0, 0, 0)}100%{box-shadow:0 0 0 0 rgba(255, 0, 0, 0)}}@media screen and (max-width: 768px){.pc-layout{width:95%;}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}}",_e=`.markdown-body{--base-size-4:4px;--base-size-8:8px;--base-size-16:16px;--base-size-24:24px;--base-size-40:40px;--base-text-weight-normal:400;--base-text-weight-medium:500;--base-text-weight-semibold:600;--fontStack-monospace:ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;--fgColor-accent:Highlight}@media (prefers-color-scheme: dark){.markdown-body,[data-theme="dark"]{color-scheme:dark;--focus-outlineColor:#1f6feb;--fgColor-default:#f0f6fc;--fgColor-muted:#9198a1;--fgColor-accent:#4493f8;--fgColor-success:#3fb950;--fgColor-attention:#d29922;--fgColor-danger:#f85149;--fgColor-done:#ab7df8;--bgColor-default:#0d1117;--bgColor-muted:#151b23;--bgColor-neutral-muted:#656c7633;--bgColor-attention-muted:#bb800926;--borderColor-default:#3d444d;--borderColor-muted:#3d444db3;--borderColor-neutral-muted:#3d444db3;--borderColor-accent-emphasis:#1f6feb;--borderColor-success-emphasis:#238636;--borderColor-attention-emphasis:#9e6a03;--borderColor-danger-emphasis:#da3633;--borderColor-done-emphasis:#8957e5;--color-prettylights-syntax-comment:#9198a1;--color-prettylights-syntax-constant:#79c0ff;--color-prettylights-syntax-constant-other-reference-link:#a5d6ff;--color-prettylights-syntax-entity:#d2a8ff;--color-prettylights-syntax-storage-modifier-import:#f0f6fc;--color-prettylights-syntax-entity-tag:#7ee787;--color-prettylights-syntax-keyword:#ff7b72;--color-prettylights-syntax-string:#a5d6ff;--color-prettylights-syntax-variable:#ffa657;--color-prettylights-syntax-brackethighlighter-unmatched:#f85149;--color-prettylights-syntax-brackethighlighter-angle:#9198a1;--color-prettylights-syntax-invalid-illegal-text:#f0f6fc;--color-prettylights-syntax-invalid-illegal-bg:#8e1519;--color-prettylights-syntax-carriage-return-text:#f0f6fc;--color-prettylights-syntax-carriage-return-bg:#b62324;--color-prettylights-syntax-string-regexp:#7ee787;--color-prettylights-syntax-markup-list:#f2cc60;--color-prettylights-syntax-markup-heading:#1f6feb;--color-prettylights-syntax-markup-italic:#f0f6fc;--color-prettylights-syntax-markup-bold:#f0f6fc;--color-prettylights-syntax-markup-deleted-text:#ffdcd7;--color-prettylights-syntax-markup-deleted-bg:#67060c;--color-prettylights-syntax-markup-inserted-text:#aff5b4;--color-prettylights-syntax-markup-inserted-bg:#033a16;--color-prettylights-syntax-markup-changed-text:#ffdfb6;--color-prettylights-syntax-markup-changed-bg:#5a1e02;--color-prettylights-syntax-markup-ignored-text:#f0f6fc;--color-prettylights-syntax-markup-ignored-bg:#1158c7;--color-prettylights-syntax-meta-diff-range:#d2a8ff;--color-prettylights-syntax-sublimelinter-gutter-mark:#3d444d}}@media (prefers-color-scheme: light){.markdown-body,[data-theme="light"]{color-scheme:light;--focus-outlineColor:#0969da;--fgColor-default:#1f2328;--fgColor-muted:#59636e;--fgColor-accent:#0969da;--fgColor-success:#1a7f37;--fgColor-attention:#9a6700;--fgColor-danger:#d1242f;--fgColor-done:#8250df;--bgColor-default:#ffffff;--bgColor-muted:#f6f8fa;--bgColor-neutral-muted:#818b981f;--bgColor-attention-muted:#fff8c5;--borderColor-default:#d1d9e0;--borderColor-muted:#d1d9e0b3;--borderColor-neutral-muted:#d1d9e0b3;--borderColor-accent-emphasis:#0969da;--borderColor-success-emphasis:#1a7f37;--borderColor-attention-emphasis:#9a6700;--borderColor-danger-emphasis:#cf222e;--borderColor-done-emphasis:#8250df;--color-prettylights-syntax-comment:#59636e;--color-prettylights-syntax-constant:#0550ae;--color-prettylights-syntax-constant-other-reference-link:#0a3069;--color-prettylights-syntax-entity:#6639ba;--color-prettylights-syntax-storage-modifier-import:#1f2328;--color-prettylights-syntax-entity-tag:#0550ae;--color-prettylights-syntax-keyword:#cf222e;--color-prettylights-syntax-string:#0a3069;--color-prettylights-syntax-variable:#953800;--color-prettylights-syntax-brackethighlighter-unmatched:#82071e;--color-prettylights-syntax-brackethighlighter-angle:#59636e;--color-prettylights-syntax-invalid-illegal-text:#f6f8fa;--color-prettylights-syntax-invalid-illegal-bg:#82071e;--color-prettylights-syntax-carriage-return-text:#f6f8fa;--color-prettylights-syntax-carriage-return-bg:#cf222e;--color-prettylights-syntax-string-regexp:#116329;--color-prettylights-syntax-markup-list:#3b2300;--color-prettylights-syntax-markup-heading:#0550ae;--color-prettylights-syntax-markup-italic:#1f2328;--color-prettylights-syntax-markup-bold:#1f2328;--color-prettylights-syntax-markup-deleted-text:#82071e;--color-prettylights-syntax-markup-deleted-bg:#ffebe9;--color-prettylights-syntax-markup-inserted-text:#116329;--color-prettylights-syntax-markup-inserted-bg:#dafbe1;--color-prettylights-syntax-markup-changed-text:#953800;--color-prettylights-syntax-markup-changed-bg:#ffd8b5;--color-prettylights-syntax-markup-ignored-text:#d1d9e0;--color-prettylights-syntax-markup-ignored-bg:#0550ae;--color-prettylights-syntax-meta-diff-range:#8250df;--color-prettylights-syntax-sublimelinter-gutter-mark:#818b98}}.markdown-body{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;margin:0;font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";font-size:16px;line-height:1.5;word-wrap:break-word}.markdown-body .octicon{display:inline-block;fill:currentColor;vertical-align:text-bottom}.markdown-body h1:hover .anchor .octicon-link:before,.markdown-body h2:hover .anchor .octicon-link:before,.markdown-body h3:hover .anchor .octicon-link:before,.markdown-body h4:hover .anchor .octicon-link:before,.markdown-body h5:hover .anchor .octicon-link:before,.markdown-body h6:hover .anchor .octicon-link:before{width:16px;height:16px;content:' ';display:inline-block;background-color:currentColor;-webkit-mask-image:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");mask-image:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>")}.markdown-body details,.markdown-body figcaption,.markdown-body figure{display:block}.markdown-body summary{display:list-item}.markdown-body [hidden]{display:none !important}.markdown-body a{background-color:transparent;color:var(--fgColor-accent);text-decoration:none}.markdown-body abbr[title]{border-bottom:none;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}.markdown-body b,.markdown-body strong{font-weight:var(--base-text-weight-semibold, 600)}.markdown-body dfn{font-style:italic}.markdown-body h1{margin:.67em 0;font-weight:var(--base-text-weight-semibold, 600);padding-bottom:.3em;font-size:1.5em;border-bottom:1px solid var(--borderColor-muted)}.markdown-body mark{background-color:var(--bgColor-attention-muted);color:var(--fgColor-default)}.markdown-body small{font-size:90%}.markdown-body sub,.markdown-body sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}.markdown-body sub{bottom:-0.25em}.markdown-body sup{top:-0.5em}.markdown-body img{border-style:none;max-width:100%;box-sizing:content-box}.markdown-body code,.markdown-body kbd,.markdown-body pre,.markdown-body samp{font-family:monospace;font-size:1em}.markdown-body figure{margin:1em var(--base-size-40)}.markdown-body hr{box-sizing:content-box;overflow:hidden;background:transparent;border-bottom:1px solid var(--borderColor-muted);height:.25em;padding:0;margin:var(--base-size-24) 0;background-color:var(--borderColor-default);border:0}.markdown-body input{font:inherit;margin:0;overflow:visible;font-family:inherit;font-size:inherit;line-height:inherit}.markdown-body [type=button],.markdown-body [type=reset],.markdown-body [type=submit]{-webkit-appearance:button;appearance:button}.markdown-body [type=checkbox],.markdown-body [type=radio]{box-sizing:border-box;padding:0}.markdown-body [type=number]::-webkit-inner-spin-button,.markdown-body [type=number]::-webkit-outer-spin-button{height:auto}.markdown-body [type=search]::-webkit-search-cancel-button,.markdown-body [type=search]::-webkit-search-decoration{-webkit-appearance:none;appearance:none}.markdown-body ::-webkit-input-placeholder{color:inherit;opacity:.54}.markdown-body ::-webkit-file-upload-button{-webkit-appearance:button;appearance:button;font:inherit}.markdown-body a:hover{text-decoration:underline}.markdown-body ::placeholder{color:var(--fgColor-muted);opacity:1}.markdown-body hr::before{display:table;content:""}.markdown-body hr::after{display:table;clear:both;content:""}.markdown-body table{border-spacing:0;border-collapse:collapse;display:block;width:max-content;max-width:100%;overflow:auto;font-variant:tabular-nums}.markdown-body td,.markdown-body th{padding:0}.markdown-body details summary{cursor:pointer}.markdown-body a:focus,.markdown-body [role=button]:focus,.markdown-body input[type=radio]:focus,.markdown-body input[type=checkbox]:focus{outline:2px solid var(--focus-outlineColor);outline-offset:-2px;box-shadow:none}.markdown-body a:focus:not(:focus-visible),.markdown-body [role=button]:focus:not(:focus-visible),.markdown-body input[type=radio]:focus:not(:focus-visible),.markdown-body input[type=checkbox]:focus:not(:focus-visible){outline:solid 1px transparent}.markdown-body a:focus-visible,.markdown-body [role=button]:focus-visible,.markdown-body input[type=radio]:focus-visible,.markdown-body input[type=checkbox]:focus-visible{outline:2px solid var(--focus-outlineColor);outline-offset:-2px;box-shadow:none}.markdown-body a:not([class]):focus,.markdown-body a:not([class]):focus-visible,.markdown-body input[type=radio]:focus,.markdown-body input[type=radio]:focus-visible,.markdown-body input[type=checkbox]:focus,.markdown-body input[type=checkbox]:focus-visible{outline-offset:0}.markdown-body kbd{display:inline-block;padding:var(--base-size-4);font:11px var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);line-height:10px;color:var(--fgColor-default);vertical-align:middle;background-color:var(--bgColor-muted);border:solid 1px var(--borderColor-neutral-muted);border-bottom-color:var(--borderColor-neutral-muted);border-radius:6px;box-shadow:inset 0 -1px 0 var(--borderColor-neutral-muted)}.markdown-body h1,.markdown-body h2,.markdown-body h3,.markdown-body h4,.markdown-body h5,.markdown-body h6{margin-top:var(--base-size-24);margin-bottom:var(--base-size-16);font-weight:var(--base-text-weight-semibold, 600);line-height:1.25}.markdown-body h2{font-weight:var(--base-text-weight-semibold, 600);padding-bottom:.3em;font-size:1.2em;border-bottom:1px solid var(--borderColor-muted)}.markdown-body h3{font-weight:var(--base-text-weight-semibold, 600);font-size:1.05em}.markdown-body h4{font-weight:var(--base-text-weight-semibold, 600);font-size:0.875em}.markdown-body h5{font-weight:var(--base-text-weight-semibold, 600);font-size:.85em}.markdown-body h6{font-weight:var(--base-text-weight-semibold, 600);font-size:.80em;color:var(--fgColor-muted)}.markdown-body p{margin-top:0;margin-bottom:10px}.markdown-body blockquote{margin:0;padding:0 1em;color:var(--fgColor-muted);border-left:.25em solid var(--borderColor-default)}.markdown-body ul,.markdown-body ol{margin-top:0;margin-bottom:0;padding-left:2em}.markdown-body ol ol,.markdown-body ul ol{list-style-type:lower-roman}.markdown-body ul ul ol,.markdown-body ul ol ol,.markdown-body ol ul ol,.markdown-body ol ol ol{list-style-type:lower-alpha}.markdown-body dd{margin-left:0}.markdown-body tt,.markdown-body code,.markdown-body samp{font-family:var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);font-size:12px}.markdown-body pre{margin-top:0;margin-bottom:0;font-family:var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);font-size:12px;word-wrap:normal}.markdown-body .octicon{display:inline-block;overflow:visible !important;vertical-align:text-bottom;fill:currentColor}.markdown-body input::-webkit-outer-spin-button,.markdown-body input::-webkit-inner-spin-button{margin:0;appearance:none}.markdown-body .mr-2{margin-right:var(--base-size-8, 8px) !important}.markdown-body::before{display:table;content:""}.markdown-body::after{display:table;clear:both;content:""}.markdown-body>*:first-child{margin-top:0 !important}.markdown-body>*:last-child{margin-bottom:0 !important}.markdown-body a:not([href]){color:inherit;text-decoration:none}.markdown-body .absent{color:var(--fgColor-danger)}.markdown-body .anchor{float:left;padding-right:var(--base-size-4);margin-left:-20px;line-height:1}.markdown-body .anchor:focus{outline:none}.markdown-body p,.markdown-body blockquote,.markdown-body ul,.markdown-body ol,.markdown-body dl,.markdown-body table,.markdown-body pre,.markdown-body details{margin-top:0;margin-bottom:var(--base-size-16);font-size:16px;font-weight:400}.markdown-body blockquote>:first-child{margin-top:0}.markdown-body blockquote>:last-child{margin-bottom:0}.markdown-body h1 .octicon-link,.markdown-body h2 .octicon-link,.markdown-body h3 .octicon-link,.markdown-body h4 .octicon-link,.markdown-body h5 .octicon-link,.markdown-body h6 .octicon-link{color:var(--fgColor-default);vertical-align:middle;visibility:hidden}.markdown-body h1:hover .anchor,.markdown-body h2:hover .anchor,.markdown-body h3:hover .anchor,.markdown-body h4:hover .anchor,.markdown-body h5:hover .anchor,.markdown-body h6:hover .anchor{text-decoration:none}.markdown-body h1:hover .anchor .octicon-link,.markdown-body h2:hover .anchor .octicon-link,.markdown-body h3:hover .anchor .octicon-link,.markdown-body h4:hover .anchor .octicon-link,.markdown-body h5:hover .anchor .octicon-link,.markdown-body h6:hover .anchor .octicon-link{visibility:visible}.markdown-body h1 tt,.markdown-body h1 code,.markdown-body h2 tt,.markdown-body h2 code,.markdown-body h3 tt,.markdown-body h3 code,.markdown-body h4 tt,.markdown-body h4 code,.markdown-body h5 tt,.markdown-body h5 code,.markdown-body h6 tt,.markdown-body h6 code{padding:0 .2em;font-size:inherit}.markdown-body summary h1,.markdown-body summary h2,.markdown-body summary h3,.markdown-body summary h4,.markdown-body summary h5,.markdown-body summary h6{display:inline-block}.markdown-body summary h1 .anchor,.markdown-body summary h2 .anchor,.markdown-body summary h3 .anchor,.markdown-body summary h4 .anchor,.markdown-body summary h5 .anchor,.markdown-body summary h6 .anchor{margin-left:-40px}.markdown-body summary h1,.markdown-body summary h2{padding-bottom:0;border-bottom:0}.markdown-body ul.no-list,.markdown-body ol.no-list{padding:0;list-style-type:none}.markdown-body ol[type="a s"]{list-style-type:lower-alpha}.markdown-body ol[type="A s"]{list-style-type:upper-alpha}.markdown-body ol[type="i s"]{list-style-type:lower-roman}.markdown-body ol[type="I s"]{list-style-type:upper-roman}.markdown-body ol[type="1"]{list-style-type:decimal}.markdown-body div>ol:not([type]){list-style-type:decimal}.markdown-body ul ul,.markdown-body ul ol,.markdown-body ol ol,.markdown-body ol ul{margin-top:0;margin-bottom:0}.markdown-body li>p{margin-top:var(--base-size-16)}.markdown-body li+li{margin-top:.25em}.markdown-body dl{padding:0}.markdown-body dl dt{padding:0;margin-top:var(--base-size-16);font-size:1em;font-style:italic;font-weight:var(--base-text-weight-semibold, 600)}.markdown-body dl dd{padding:0 var(--base-size-16);margin-bottom:var(--base-size-16)}.markdown-body table th{font-weight:var(--base-text-weight-semibold, 600)}.markdown-body table th,.markdown-body table td{padding:6px 13px;border:1px solid var(--borderColor-default)}.markdown-body table td>:last-child{margin-bottom:0}.markdown-body table tr{background-color:var(--bgColor-default);border-top:1px solid var(--borderColor-muted)}.markdown-body table tr:nth-child(2n){background-color:var(--bgColor-muted)}.markdown-body table img{background-color:transparent}.markdown-body img[align=right]{padding-left:20px}.markdown-body img[align=left]{padding-right:20px}.markdown-body .emoji{max-width:none;vertical-align:text-top;background-color:transparent}.markdown-body span.frame{display:block;overflow:hidden}.markdown-body span.frame>span{display:block;float:left;width:auto;padding:7px;margin:13px 0 0;overflow:hidden;border:1px solid var(--borderColor-default)}.markdown-body span.frame span img{display:block;float:left}.markdown-body span.frame span span{display:block;padding:5px 0 0;clear:both;color:var(--fgColor-default)}.markdown-body span.align-center{display:block;overflow:hidden;clear:both}.markdown-body span.align-center>span{display:block;margin:13px auto 0;overflow:hidden;text-align:center}.markdown-body span.align-center span img{margin:0 auto;text-align:center}.markdown-body span.align-right{display:block;overflow:hidden;clear:both}.markdown-body span.align-right>span{display:block;margin:13px 0 0;overflow:hidden;text-align:right}.markdown-body span.align-right span img{margin:0;text-align:right}.markdown-body span.float-left{display:block;float:left;margin-right:13px;overflow:hidden}.markdown-body span.float-left span{margin:13px 0 0}.markdown-body span.float-right{display:block;float:right;margin-left:13px;overflow:hidden}.markdown-body span.float-right>span{display:block;margin:13px auto 0;overflow:hidden;text-align:right}.markdown-body code,.markdown-body tt{padding:.2em .4em;margin:0;font-size:85%;white-space:break-spaces;background-color:var(--bgColor-neutral-muted);border-radius:6px}.markdown-body code br,.markdown-body tt br{display:none}.markdown-body del code{text-decoration:inherit}.markdown-body samp{font-size:85%}.markdown-body pre code{font-size:100%}.markdown-body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:transparent;border:0}.markdown-body .highlight{margin-bottom:var(--base-size-16)}.markdown-body .highlight pre{margin-bottom:0;word-break:normal}.markdown-body .highlight pre,.markdown-body pre{padding:var(--base-size-16);overflow:auto;font-size:85%;line-height:1.45;color:var(--fgColor-default);background-color:var(--bgColor-muted);border-radius:6px}.markdown-body pre code,.markdown-body pre tt{display:inline;max-width:auto;padding:0;margin:0;overflow:visible;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}.markdown-body .csv-data td,.markdown-body .csv-data th{padding:5px;overflow:hidden;font-size:12px;line-height:1;text-align:left;white-space:nowrap}.markdown-body .csv-data .blob-num{padding:10px var(--base-size-8) 9px;text-align:right;background:var(--bgColor-default);border:0}.markdown-body .csv-data tr{border-top:0}.markdown-body .csv-data th{font-weight:var(--base-text-weight-semibold, 600);background:var(--bgColor-muted);border-top:0}.markdown-body [data-footnote-ref]::before{content:"["}.markdown-body [data-footnote-ref]::after{content:"]"}.markdown-body .footnotes{font-size:12px;color:var(--fgColor-muted);border-top:1px solid var(--borderColor-default)}.markdown-body .footnotes ol{padding-left:var(--base-size-16)}.markdown-body .footnotes ol ul{display:inline-block;padding-left:var(--base-size-16);margin-top:var(--base-size-16)}.markdown-body .footnotes li{position:relative}.markdown-body .footnotes li:target::before{position:absolute;top:calc(var(--base-size-8)*-1);right:calc(var(--base-size-8)*-1);bottom:calc(var(--base-size-8)*-1);left:calc(var(--base-size-24)*-1);pointer-events:none;content:"";border:2px solid var(--borderColor-accent-emphasis);border-radius:6px}.markdown-body .footnotes li:target{color:var(--fgColor-default)}.markdown-body .footnotes .data-footnote-backref g-emoji{font-family:monospace}.markdown-body body:has(:modal){padding-right:var(--dialog-scrollgutter) !important}.markdown-body .pl-c{color:var(--color-prettylights-syntax-comment)}.markdown-body .pl-c1,.markdown-body .pl-s .pl-v{color:var(--color-prettylights-syntax-constant)}.markdown-body .pl-e,.markdown-body .pl-en{color:var(--color-prettylights-syntax-entity)}.markdown-body .pl-smi,.markdown-body .pl-s .pl-s1{color:var(--color-prettylights-syntax-storage-modifier-import)}.markdown-body .pl-ent{color:var(--color-prettylights-syntax-entity-tag)}.markdown-body .pl-k{color:var(--color-prettylights-syntax-keyword)}.markdown-body .pl-s,.markdown-body .pl-pds,.markdown-body .pl-s .pl-pse .pl-s1,.markdown-body .pl-sr,.markdown-body .pl-sr .pl-cce,.markdown-body .pl-sr .pl-sre,.markdown-body .pl-sr .pl-sra{color:var(--color-prettylights-syntax-string)}.markdown-body .pl-v,.markdown-body .pl-smw{color:var(--color-prettylights-syntax-variable)}.markdown-body .pl-bu{color:var(--color-prettylights-syntax-brackethighlighter-unmatched)}.markdown-body .pl-ii{color:var(--color-prettylights-syntax-invalid-illegal-text);background-color:var(--color-prettylights-syntax-invalid-illegal-bg)}.markdown-body .pl-c2{color:var(--color-prettylights-syntax-carriage-return-text);background-color:var(--color-prettylights-syntax-carriage-return-bg)}.markdown-body .pl-sr .pl-cce{font-weight:bold;color:var(--color-prettylights-syntax-string-regexp)}.markdown-body .pl-ml{color:var(--color-prettylights-syntax-markup-list)}.markdown-body .pl-mh,.markdown-body .pl-mh .pl-en,.markdown-body .pl-ms{font-weight:bold;color:var(--color-prettylights-syntax-markup-heading)}.markdown-body .pl-mi{font-style:italic;color:var(--color-prettylights-syntax-markup-italic)}.markdown-body .pl-mb{font-weight:bold;color:var(--color-prettylights-syntax-markup-bold)}.markdown-body .pl-md{color:var(--color-prettylights-syntax-markup-deleted-text);background-color:var(--color-prettylights-syntax-markup-deleted-bg)}.markdown-body .pl-mi1{color:var(--color-prettylights-syntax-markup-inserted-text);background-color:var(--color-prettylights-syntax-markup-inserted-bg)}.markdown-body .pl-mc{color:var(--color-prettylights-syntax-markup-changed-text);background-color:var(--color-prettylights-syntax-markup-changed-bg)}.markdown-body .pl-mi2{color:var(--color-prettylights-syntax-markup-ignored-text);background-color:var(--color-prettylights-syntax-markup-ignored-bg)}.markdown-body .pl-mdr{font-weight:bold;color:var(--color-prettylights-syntax-meta-diff-range)}.markdown-body .pl-ba{color:var(--color-prettylights-syntax-brackethighlighter-angle)}.markdown-body .pl-sg{color:var(--color-prettylights-syntax-sublimelinter-gutter-mark)}.markdown-body .pl-corl{text-decoration:underline;color:var(--color-prettylights-syntax-constant-other-reference-link)}.markdown-body [role=button]:focus:not(:focus-visible),.markdown-body [role=tabpanel][tabindex="0"]:focus:not(:focus-visible),.markdown-body button:focus:not(:focus-visible),.markdown-body summary:focus:not(:focus-visible),.markdown-body a:focus:not(:focus-visible){outline:none;box-shadow:none}.markdown-body [tabindex="0"]:focus:not(:focus-visible),.markdown-body details-dialog:focus:not(:focus-visible){outline:none}.markdown-body g-emoji{display:inline-block;min-width:1ch;font-family:"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";font-size:1em;font-style:normal !important;font-weight:var(--base-text-weight-normal, 400);line-height:1;vertical-align:-0.075em}.markdown-body g-emoji img{width:1em;height:1em}.markdown-body .task-list-item{list-style-type:none}.markdown-body .task-list-item label{font-weight:var(--base-text-weight-normal, 400)}.markdown-body .task-list-item.enabled label{cursor:pointer}.markdown-body .task-list-item+.task-list-item{margin-top:var(--base-size-4)}.markdown-body .task-list-item .handle{display:none}.markdown-body .task-list-item-checkbox{margin:0 .2em .25em -1.4em;vertical-align:middle}.markdown-body ul:dir(rtl) .task-list-item-checkbox{margin:0 -1.6em .25em .2em}.markdown-body ol:dir(rtl) .task-list-item-checkbox{margin:0 -1.6em .25em .2em}.markdown-body .contains-task-list:hover .task-list-item-convert-container,.markdown-body .contains-task-list:focus-within .task-list-item-convert-container{display:block;width:auto;height:24px;overflow:visible;clip:auto}.markdown-body ::-webkit-calendar-picker-indicator{filter:invert(50%)}.markdown-body .markdown-alert{padding:var(--base-size-8) var(--base-size-16);margin-bottom:var(--base-size-16);color:inherit;border-left:.25em solid var(--borderColor-default)}.markdown-body .markdown-alert>:first-child{margin-top:0}.markdown-body .markdown-alert>:last-child{margin-bottom:0}.markdown-body .markdown-alert .markdown-alert-title{display:flex;font-weight:var(--base-text-weight-medium, 500);align-items:center;line-height:1}.markdown-body .markdown-alert.markdown-alert-note{border-left-color:var(--borderColor-accent-emphasis)}.markdown-body .markdown-alert.markdown-alert-note .markdown-alert-title{color:var(--fgColor-accent)}.markdown-body .markdown-alert.markdown-alert-important{border-left-color:var(--borderColor-done-emphasis)}.markdown-body .markdown-alert.markdown-alert-important .markdown-alert-title{color:var(--fgColor-done)}.markdown-body .markdown-alert.markdown-alert-warning{border-left-color:var(--borderColor-attention-emphasis)}.markdown-body .markdown-alert.markdown-alert-warning .markdown-alert-title{color:var(--fgColor-attention)}.markdown-body .markdown-alert.markdown-alert-tip{border-left-color:var(--borderColor-success-emphasis)}.markdown-body .markdown-alert.markdown-alert-tip .markdown-alert-title{color:var(--fgColor-success)}.markdown-body .markdown-alert.markdown-alert-caution{border-left-color:var(--borderColor-danger-emphasis)}.markdown-body .markdown-alert.markdown-alert-caution .markdown-alert-title{color:var(--fgColor-danger)}.markdown-body>*:first-child>.heading-element:first-child{margin-top:0 !important}.markdown-body .highlight pre:has(+.zeroclipboard-container){min-height:52px}`,De=class{constructor(e){i(this,"modalTitle","在线客服");i(this,"token");i(this,"isOpen",!1);i(this,"messages",[]);i(this,"modalClosed");i(this,"icon");i(this,"zIndex");i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"currentAssistantMessage","");i(this,"isLoading",!1);i(this,"currentStreamingMessage",null);i(this,"shouldAutoScroll",!0);i(this,"isLoadingHistory",!1);i(this,"streamComplete");i(this,"conversationStart");i(this,"isUploading",!1);i(this,"defaultQuery","你好！聘才猫");i(this,"maxRecordingTime",120);i(this,"countdownWarningTime",30);i(this,"isRecording",!1);i(this,"recordingStream",null);i(this,"recordedBlob",null);i(this,"mediaRecorder",null);i(this,"recordingTimeLeft",0);i(this,"showRecordingUI",!1);i(this,"recordingTimer",null);i(this,"recordingStartTime",0);i(this,"waitingToRecord",!1);i(this,"waitingTimer",null);i(this,"waitingTimeLeft",10);i(this,"videoRef",null);i(this,"currentQuestionNumber",0);i(this,"interviewComplete");i(this,"SCROLL_THRESHOLD",20);i(this,"showCountdownWarning",!1);i(this,"fullscreen",!1);i(this,"isUploadingVideo",!1);i(this,"isPlayingAudio",!1);i(this,"audioUrl",null);i(this,"audioElement",null);i(this,"recordingError");i(this,"recordingStatusChange");i(this,"tokenInvalid");i(this,"enableTTS",!1);i(this,"enableVoice",!1);i(this,"interviewMode","video");i(this,"textAnswer","");i(this,"isSubmittingText",!1);i(this,"customInputs",{});i(this,"botId");i(this,"isRecordingAudio",!1);i(this,"audioRecorder",null);i(this,"audioChunks",[]);i(this,"isConvertingAudio",!1);i(this,"audioRecordingTimeLeft",60);i(this,"audioRecordingTimer",null);i(this,"audioRecordingStartTime",0);i(this,"maxAudioRecordingTime",60);i(this,"userAvatar","https://pub.pincaimao.com/static/common/i_pcm_logo.png");i(this,"assistantAvatar");i(this,"agentLogo","");i(this,"isTaskCompleted",!1);i(this,"tokenInvalidListener");i(this,"showCopyButton",!0);i(this,"showFeedbackButtons",!0);i(this,"filePreviewMode","window");i(this,"isDrawerOpen",!1);i(this,"previewUrl","");i(this,"previewFileName","");i(this,"previewContentType","file");i(this,"previewContent","");i(this,"isUserScrolling",!1);i(this,"handleClose",()=>{this.stopRecording(),this.modalClosed.emit()});i(this,"handleScroll",()=>{var e;if(this.isUserScrolling){const t=(e=this.hostElement.shadowRoot)==null?void 0:e.querySelector(".chat-history");if(!t)return;const{scrollTop:n,scrollHeight:r,clientHeight:s}=t,a=r-n-s;this.shouldAutoScroll=a<=this.SCROLL_THRESHOLD}});i(this,"handleTouchStart",()=>{this.isUserScrolling=!0});i(this,"handleTouchEnd",()=>{setTimeout(()=>{this.isUserScrolling=!1},100)});i(this,"_wheelTimer",null);i(this,"handleWheel",()=>{this.isUserScrolling=!0,this._wheelTimer&&clearTimeout(this._wheelTimer),this._wheelTimer=setTimeout(()=>{this.isUserScrolling=!1},150)});i(this,"handlePlayAudio",async()=>{this.audioUrl&&(await this.playAudio(this.audioUrl),this.interviewMode==="video"&&this.startWaitingToRecord())});i(this,"handleTextInputChange",e=>{const t=e.target;this.textAnswer=t.value});i(this,"handleKeyDown",e=>{if(e.key==="Enter"){if(e.ctrlKey)return;e.preventDefault(),this.textAnswer.trim()&&!this.isSubmittingText&&!this.isLoading&&!this.currentStreamingMessage&&!this.waitingToRecord&&!this.isPlayingAudio&&this.submitTextAnswer()}});i(this,"submitTextAnswer",async()=>{if(!(!this.textAnswer.trim()||this.isSubmittingText)){this.isSubmittingText=!0;try{const e=this.textAnswer;this.textAnswer="",await this.sendMessageToAPI(e)}catch(e){console.error("提交文本回答失败:",e),x.captureError(e,{action:"submitTextAnswer",component:"pcm-app-chat-modal",title:"提交文本回答失败"}),y.emitError({error:e,message:"提交文本回答失败"})}finally{this.isSubmittingText=!1}}});i(this,"handleVoiceInputClick",()=>{this.isRecordingAudio?this.stopAudioRecording():navigator.mediaDevices.getUserMedia({audio:!0}).then(e=>{this.startRecordingWithStream(e)}).catch(e=>{console.error("麦克风权限请求失败:",e),x.captureError(e,{action:"handleVoiceInputClick",component:"pcm-app-chat-modal",title:"麦克风权限请求失败"}),y.emitError({error:e,message:"麦克风权限请求失败"})})});i(this,"handleFilePreviewRequest",e=>{const{url:t,fileName:n,content:r,contentType:s}=e.detail;this.previewFileName=n||"内容预览",this.previewContentType=s,this.previewUrl=t,this.previewContent=r||"",this.isDrawerOpen=!0});S(this,e),this.modalClosed=m(this,"modalClosed"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.recordingError=m(this,"recordingError"),this.recordingStatusChange=m(this,"recordingStatusChange"),this.tokenInvalid=m(this,"tokenInvalid"),F.setOptions({breaks:!0,gfm:!0})}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}componentWillLoad(){if(this.zIndex)z.setItem("modal-zIndex",this.zIndex);else{const e=z.getItem("modal-zIndex");e?this.zIndex=e:(this.zIndex=1e3,z.setItem("modal-zIndex",1e3))}this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},document.addEventListener("pcm-token-invalid",this.tokenInvalidListener),this.customInputs||(this.customInputs={}),!this.assistantAvatar&&this.botId&&this.fetchAgentLogo(),this.isOpen&&(this.conversationId?setTimeout(()=>this.loadHistoryMessages(),0):setTimeout(()=>{this.sendMessageToAPI(this.defaultQuery)},0))}async fetchAgentLogo(){if(this.botId)try{const e=await fe(this.botId);e&&e.logo&&(this.agentLogo=e.logo)}catch(e){x.captureError(e,{action:"fetchAgentLogo",component:"pcm-app-chat-modal",title:"获取智能体信息失败"}),y.emitError({error:e,message:"获取智能体信息失败"})}}async sendMessageToAPI(e,t){this.isLoading=!0;let n="",r="";const s=new Date,a=`${s.getHours()}:${s.getMinutes().toString().padStart(2,"0")}`,l=e.trim()||"请开始",d={id:`temp-${Date.now()}`,conversation_id:this.conversationId,parent_message_id:"00000000-0000-0000-0000-000000000000",inputs:this.customInputs||{},query:l,answer:"",message_files:[],feedback:{},retriever_resources:[],created_at:Math.floor(Date.now()/1e3).toString(),agent_thoughts:[],status:"normal",error:null,time:a,isStreaming:!0};this.currentStreamingMessage=d,setTimeout(()=>{this.shouldAutoScroll=!0,this.scrollToBottom()},200);const c={response_mode:"streaming",conversation_id:this.conversationId,query:l,bot_id:this.botId};c.inputs={...this.customInputs},t&&(c.inputs.video_url=t),await te({url:"/sdk/v1/chat/chat-messages",method:"POST",data:c,onMessage:p=>{if(p.conversation_id&&!this.conversationId&&(this.conversationId=p.conversation_id,this.conversationStart.emit({conversation_id:p.conversation_id,event:p.event,message_id:p.message_id,id:p.id})),p.event==="node_finished"&&p.data.inputs&&p.data.inputs.LLMText&&(r=p.data.inputs.LLMText),p.event==="node_finished"&&p.data.title&&p.data.title.includes("聘才猫任务结束")&&(this.isTaskCompleted=!0,this.interviewComplete.emit({conversation_id:this.conversationId,current_question_number:this.currentQuestionNumber})),p.event==="message"&&(p.event==="agent_message"||p.event==="message")&&p.answer){n+=p.answer;const u={...this.currentStreamingMessage,answer:n,id:p.message_id||this.currentStreamingMessage.id,isStreaming:!0,parent_message_id:p.parent_message_id||this.currentStreamingMessage.parent_message_id,retriever_resources:p.retriever_resources||this.currentStreamingMessage.retriever_resources,agent_thoughts:p.agent_thoughts||this.currentStreamingMessage.agent_thoughts};this.currentStreamingMessage=u,this.scrollToBottom()}p.event==="message_end"&&this.streamComplete.emit({conversation_id:p.conversation_id||"",event:p.event,message_id:p.message_id,id:p.id})},onError:p=>{console.error("发生错误:",p),y.emitError({error:p,message:"消息发送失败，请稍后再试"}),x.captureError(p,{action:"sendMessageToAPI",component:"pcm-app-chat-modal",title:"消息发送失败"}),this.messages=[...this.messages,{...d,answer:"抱歉，发生了错误，请稍后再试。",error:p,isStreaming:!1}],this.currentStreamingMessage=null,this.isLoading=!1},onComplete:async()=>{this.isLoading=!1,setTimeout(()=>{this.customInputs&&Object.keys(this.customInputs).forEach(u=>{delete this.customInputs[u]})},1e3);const p=this.currentStreamingMessage;if(p.isStreaming=!1,this.currentStreamingMessage=null,this.messages=[...this.messages,p],this.currentQuestionNumber++,!this.isTaskCompleted&&p&&p.answer){const u=r||p.answer;if(u&&this.enableTTS){const g=await Y(u);this.enableVoice?(await this.playAudio(g),this.interviewMode==="video"&&this.startWaitingToRecord()):this.audioUrl=g}else this.interviewMode==="video"&&this.startWaitingToRecord()}}})}scrollToBottom(){var t;if(!this.shouldAutoScroll)return;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".chat-history");e&&this.isOpen&&(e.scrollTop=e.scrollHeight)}async loadHistoryMessages(){if(this.conversationId){this.isLoadingHistory=!0;try{const e=await M({url:"/sdk/v1/chat/messages",method:"GET",data:{conversation_id:this.conversationId,bot_id:this.botId,limit:20}});if(e.success&&e.data){const n=(e.data.data||[]).map(r=>{const s=new Date(parseInt(r.created_at)*1e3),a=s.getHours().toString().padStart(2,"0"),l=s.getMinutes().toString().padStart(2,"0"),d=`${a}:${l}`;return{id:r.id,conversation_id:r.conversation_id,parent_message_id:r.parent_message_id||"00000000-0000-0000-0000-000000000000",inputs:r.inputs||{},query:r.query||"",answer:r.answer||"",message_files:r.message_files||[],feedback:r.feedback||{},retriever_resources:r.retriever_resources||[],created_at:r.created_at,agent_thoughts:r.agent_thoughts||[],status:r.status||"normal",error:r.error,time:d,isStreaming:!1}});this.messages=n}}catch(e){console.error("加载历史消息失败:",e),x.captureError(e,{action:"loadHistoryMessages",component:"pcm-app-chat-modal",title:"加载历史消息失败"}),y.emitError({error:e,message:"加载历史消息失败，请刷新重试"})}finally{this.isLoadingHistory=!1,setTimeout(()=>{this.shouldAutoScroll=!0,this.scrollToBottom()},200)}}}startWaitingToRecord(){this.interviewMode==="video"&&(this.waitingTimer&&clearInterval(this.waitingTimer),this.recordingTimer&&clearInterval(this.recordingTimer),this.waitingToRecord=!0,this.waitingTimeLeft=10,this.waitingTimer=setInterval(()=>{this.waitingTimeLeft--,this.waitingTimeLeft<=0&&(clearInterval(this.waitingTimer),this.waitingTimer=null,this.waitingToRecord=!1,this.startRecording())},1e3))}async startRecording(){try{const e=await navigator.mediaDevices.getUserMedia({audio:!0,video:{width:{ideal:1280},height:{ideal:720},frameRate:{ideal:30}}});this.recordingStream=e,this.showRecordingUI=!0,this.showCountdownWarning=!1,this.videoRef=null,this.setupVideoPreview(e);const t=this.getSupportedMimeType();let n;try{n=new MediaRecorder(e,{mimeType:t,videoBitsPerSecond:8e5,audioBitsPerSecond:64e3})}catch(s){console.warn("指定的MIME类型不受支持，使用默认设置:",s);try{n=new MediaRecorder(e,{videoBitsPerSecond:8e5,audioBitsPerSecond:64e3})}catch{try{n=new MediaRecorder(e)}catch(l){this.recordingError.emit({type:"recorder_creation_failed",message:"无法创建媒体录制器，您的浏览器可能不支持此功能",details:l}),this.showRecordingUI=!1;return}}}this.mediaRecorder=n;const r=[];n.ondataavailable=s=>{s.data.size>0&&r.push(s.data)},n.onerror=s=>{this.recordingError.emit({type:"recording_error",message:"录制过程中发生错误",details:s}),this.stopRecording()},n.onstop=()=>{try{const s=t||"video/mp4",a=new Blob(r,{type:s});if(a.size===0){this.recordingError.emit({type:"empty_recording",message:"录制的视频为空"}),this.showRecordingUI=!1;return}this.recordedBlob=a,this.recordingStatusChange.emit({status:"stopped",details:{duration:Math.floor((Date.now()-this.recordingStartTime)/1e3),size:a.size,type:a.type}}),this.uploadRecordedVideo()}catch(s){this.recordingError.emit({type:"processing_error",message:"处理录制视频时出错",details:s}),this.showRecordingUI=!1,x.captureError(s,{action:"uploadRecordedVideo",component:"pcm-hr-chat-modal",title:"处理录制视频时出错"})}};try{n.start(),this.isRecording=!0,this.recordingStartTime=Date.now(),this.recordingTimeLeft=this.maxRecordingTime,this.recordingStatusChange.emit({status:"started",details:{maxDuration:this.maxRecordingTime,mimeType:n.mimeType}})}catch(s){this.recordingError.emit({type:"start_failed",message:"开始录制失败，请检查您的设备权限",details:s}),this.showRecordingUI=!1,x.captureError(s,{action:"startRecording",component:"pcm-hr-chat-modal",title:"开始录制失败"});return}this.recordingTimer=setInterval(()=>{const s=Math.floor((Date.now()-this.recordingStartTime)/1e3);this.recordingTimeLeft=Math.max(0,this.maxRecordingTime-s),this.recordingTimeLeft<=this.countdownWarningTime&&!this.showCountdownWarning&&(this.showCountdownWarning=!0),this.recordingTimeLeft<=0&&this.stopRecording()},1e3)}catch(e){console.error("无法访问摄像头或麦克风:",e),this.showRecordingUI=!1,y.emitError({error:e,message:"无法访问摄像头或麦克风，请确保已授予权限"}),x.captureError(e,{action:"startRecording",component:"pcm-app-chat-modal",title:"无法访问摄像头或麦克风"})}}setupVideoPreview(e){setTimeout(()=>{var n;const t=(n=this.hostElement.shadowRoot)==null?void 0:n.querySelector("video");if(t&&e)try{t.srcObject=e,t.play().catch(r=>{console.error("视频播放失败:",r),x.captureError(r,{action:"setupVideoPreview",component:"pcm-app-chat-modal",title:"视频播放失败"}),y.emitError({error:r,message:"视频播放失败"})})}catch(r){console.warn("设置srcObject失败，尝试替代方法:",r);try{const s=URL.createObjectURL(e);t.src=s,t.onended=()=>{URL.revokeObjectURL(s)}}catch(s){console.error("创建对象URL失败:",s),x.captureError(s,{action:"setupVideoPreview",component:"pcm-app-chat-modal",title:"创建对象URL失败"}),y.emitError({error:s,message:"创建对象URL失败"})}}else console.warn("未找到视频元素或媒体流无效")},100)}getSupportedMimeType(){const e=["video/webm;codecs=vp8,opus","video/webm;codecs=vp9,opus","video/webm","video/mp4","video/mp4;codecs=h264,aac",""];if(!window.MediaRecorder)return console.warn("MediaRecorder API不可用"),"";for(const t of e){if(!t)return"";try{if(MediaRecorder.isTypeSupported(t))return t}catch(n){console.warn(`检查MIME类型支持时出错 ${t}:`,n)}}return console.warn("没有找到支持的MIME类型，将使用浏览器默认值"),""}stopRecording(){this.mediaRecorder&&this.isRecording&&(this.mediaRecorder.stop(),this.isRecording=!1,this.recordingTimer&&(clearInterval(this.recordingTimer),this.recordingTimer=null),this.recordingStream&&(this.recordingStream.getTracks().forEach(e=>e.stop()),this.recordingStream=null),this.videoRef=null)}async convertAudioToText(e){try{const t=await M({url:"/sdk/v1/tts/audio_to_text",method:"POST",data:{cos_key:e}});return t.success&&t.data&&t.data.text?t.data.text:(console.warn("音频转文字返回结果格式不正确"),null)}catch(t){return console.error("音频转文字错误:",t),x.captureError(t,{action:"convertAudioToText",component:"pcm-app-chat-modal",title:"音频转文字错误"}),y.emitError({error:t,message:"音频转文字错误"}),null}}async uploadRecordedVideo(){if(this.recordedBlob)try{this.isUploadingVideo=!0,this.showRecordingUI=!1;const t=`answer.${this.recordedBlob.type.includes("webm")?"webm":"mp4"}`,n=new File([this.recordedBlob],t,{type:this.recordedBlob.type}),r=await A(n,{},{tags:["other"]}),s=await this.convertAudioToText(r.cos_key);this.sendMessageToAPI(s||"下一题",r.cos_key)}catch(e){console.error("视频上传或处理错误:",e),x.captureError(e,{action:"uploadRecordedVideo",component:"pcm-app-chat-modal",title:"视频上传或处理失败"}),y.emitError({error:e,message:"视频上传或处理失败"})}finally{this.isUploadingVideo=!1,this.showRecordingUI=!1,this.recordedBlob=null}}playAudio(e){return new Promise(t=>{this.isPlayingAudio=!0,this.audioUrl=e,this.audioElement||(this.audioElement=new Audio),this.audioElement.src=e,this.audioElement.onended=()=>{this.isPlayingAudio=!1,this.audioUrl=null,t()},this.audioElement.onerror=()=>{console.error("音频播放错误"),this.isPlayingAudio=!1,this.audioUrl=null,y.emitError({error:"音频播放错误",message:"音频播放错误"}),x.captureMessage("音频播放错误",{action:"playAudio",component:"pcm-hr-chat-modal",title:"音频播放错误"}),t()},this.audioElement.play().catch(n=>{console.error("音频播放失败:",n),this.isPlayingAudio=!1,this.audioUrl=null,y.emitError({error:n,message:"音频播放失败"}),x.captureError(n,{title:"音频播放失败",action:"playAudio",component:"pcm-hr-chat-modal"}),t()})})}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.audioElement&&(this.audioElement.pause(),this.audioElement.src="",this.audioElement=null),this.audioUrl&&(URL.revokeObjectURL(this.audioUrl),this.audioUrl=null),this.waitingTimer&&(clearInterval(this.waitingTimer),this.waitingTimer=null),this.recordingTimer&&(clearInterval(this.recordingTimer),this.recordingTimer=null),this.audioRecordingTimer&&(clearInterval(this.audioRecordingTimer),this.audioRecordingTimer=null),this.stopRecording(),this.stopAudioRecording()}startRecordingWithStream(e){try{const t=this.getSupportedAudioMimeType();let n;try{n=new MediaRecorder(e,{mimeType:t})}catch(r){console.warn("指定的音频MIME类型不受支持，使用默认设置:",r);try{n=new MediaRecorder(e)}catch(s){e.getTracks().forEach(a=>a.stop()),console.error("无法创建音频录制器:",s),x.captureError(s,{action:"startRecordingWithStream",component:"pcm-app-chat-modal",title:"无法创建音频录制器"}),y.emitError({error:s,message:"无法创建音频录制器"});return}}this.audioRecorder=n,this.audioChunks=[],this.isRecordingAudio=!0,this.audioRecordingStartTime=Date.now(),this.audioRecordingTimeLeft=this.maxAudioRecordingTime,n.ondataavailable=r=>{r.data.size>0&&this.audioChunks.push(r.data)},n.onstop=()=>{e.getTracks().forEach(r=>r.stop()),this.processAudioRecording()},n.start(100),this.audioRecordingTimer=setInterval(()=>{const r=Math.floor((Date.now()-this.audioRecordingStartTime)/1e3);this.audioRecordingTimeLeft=Math.max(0,this.maxAudioRecordingTime-r),this.audioRecordingTimeLeft<=0&&this.stopAudioRecording()},1e3)}catch(t){e.getTracks().forEach(n=>n.stop()),console.error("开始录音失败:",t),x.captureError(t,{action:"startRecordingWithStream",component:"pcm-app-chat-modal",title:"开始录音失败"}),y.emitError({error:t,message:"开始录音失败，请确保麦克风设备正常工作"})}}async processAudioRecording(){var e;if(this.audioChunks.length===0){console.warn("没有录制到音频数据");return}try{this.isConvertingAudio=!0;const t=this.getSupportedAudioMimeType()||"audio/webm",n=new Blob(this.audioChunks,{type:t});if(n.size===0){console.warn("录制的音频为空"),this.isConvertingAudio=!1;return}const s=`audio_input.${t.includes("webm")?"webm":"mp3"}`,a=new File([n],s,{type:t}),l=await A(a,{},{tags:["audio"]}),d=await this.convertAudioToText(l.cos_key);if(d){const c=(e=this.hostElement.shadowRoot)==null?void 0:e.querySelector(".text-answer-input"),p=(c==null?void 0:c.selectionStart)||this.textAnswer.length,u=this.textAnswer.substring(0,p),g=this.textAnswer.substring(p),h=u.length>0&&!u.endsWith(" ")?" ":"";this.textAnswer=u+h+d+g,setTimeout(()=>{if(c){const f=p+h.length+d.length;c.focus(),c.setSelectionRange(f,f)}},0)}else console.warn("未能识别语音内容"),alert("未能识别语音内容，请重试或直接输入文字")}catch(t){console.error("处理音频录制失败:",t),x.captureError(t,{action:"processAudioRecording",component:"pcm-app-chat-modal",title:"处理音频录制失败"}),y.emitError({error:t,message:"语音识别失败，请重试"})}finally{this.isConvertingAudio=!1,this.audioChunks=[]}}getSupportedAudioMimeType(){const e=["audio/webm;codecs=opus","audio/webm","audio/mp4","audio/ogg;codecs=opus","audio/ogg",""];if(!window.MediaRecorder)return console.warn("MediaRecorder API不可用"),"";for(const t of e){if(!t)return"";try{if(MediaRecorder.isTypeSupported(t))return t}catch(n){console.warn(`检查音频MIME类型支持时出错 ${t}:`,n)}}return console.warn("没有找到支持的音频MIME类型，将使用浏览器默认值"),""}stopAudioRecording(){this.audioRecorder&&this.isRecordingAudio&&(this.audioRecorder.stop(),this.isRecordingAudio=!1,this.audioRecordingTimer&&(clearInterval(this.audioRecordingTimer),this.audioRecordingTimer=null))}render(){if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=()=>o("div",{class:"video-preview"},o("video",{autoPlay:!0,playsInline:!0,muted:!0,style:{transform:"scaleX(-1)"},ref:d=>{d&&this.recordingStream&&!this.videoRef&&(this.videoRef=d)}}),o("div",{class:{"recording-status":!0,warning:this.showCountdownWarning}},o("span",{class:"recording-dot"}),o("span",null,"录制中 ",Math.floor(this.recordingTimeLeft/60),":",(this.recordingTimeLeft%60).toString().padStart(2,"0"),this.showCountdownWarning&&" (即将自动完成)"))),s=()=>this.isTaskCompleted?o("div",{class:"placeholder-status"},o("p",null,"面试已完成，感谢您的参与！")):this.isPlayingAudio?o("div",{class:"placeholder-status"},o("p",null,"正在播放问题，请听完后准备回答...")):this.isUploadingVideo?o("div",{class:"placeholder-status"},o("p",null,"正在上传视频，请稍候...")):this.isLoading||this.currentStreamingMessage?o("div",{class:"placeholder-status"},o("p",null,"请等待题目...")):this.waitingToRecord?o("div",{class:"placeholder-status"},o("p",null,"请准备好，",this.waitingTimeLeft,"秒后将开始录制您的回答...")):o("div",{class:"placeholder-status default-status"},o("p",null,"准备中...")),a=()=>o("div",{class:"text-input-area"},o("textarea",{class:"text-answer-input",placeholder:"发消息",value:this.textAnswer,onInput:this.handleTextInputChange,onKeyDown:this.handleKeyDown,disabled:this.isRecordingAudio||this.isConvertingAudio}),o("div",{class:"input-toolbar"},o("div",{class:"toolbar-actions"},o("button",{class:{"toolbar-button":!0,recording:this.isRecordingAudio,converting:this.isConvertingAudio},title:this.isRecordingAudio?"点击停止录音":this.isConvertingAudio?"正在识别语音...":"语音输入",onClick:this.handleVoiceInputClick,disabled:this.isConvertingAudio||this.isSubmittingText||this.isLoading||!!this.currentStreamingMessage||this.waitingToRecord||this.isPlayingAudio},this.isRecordingAudio?o("div",null,o("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"currentColor"},o("path",{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}),o("path",{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"}),o("circle",{cx:"12",cy:"11",r:"4",fill:"red"}))):this.isConvertingAudio?o("div",{class:"converting-indicator"},o("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"currentColor"},o("path",{d:"M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"}))):o("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"currentColor"},o("path",{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}),o("path",{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"})))),o("div",{class:{"send-button":!0,disabled:!this.textAnswer.trim()||this.isSubmittingText||this.isLoading||!!this.currentStreamingMessage||this.waitingToRecord||this.isPlayingAudio||this.isRecordingAudio||this.isConvertingAudio},onClick:()=>{!this.textAnswer.trim()||this.isSubmittingText||this.isLoading||this.currentStreamingMessage||this.waitingToRecord||this.isPlayingAudio||this.isRecordingAudio||this.isConvertingAudio||this.submitTextAnswer()}},o("img",{src:"https://pcm-pub-1351162788.cos.ap-guangzhou.myqcloud.com/sdk/image/i_send.png",alt:"发送"})))),l=this.assistantAvatar||this.agentLogo;return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),o("div",{class:"chat-container"},o("div",{class:"chat-history",onScroll:this.handleScroll,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onWheel:this.handleWheel},this.isLoadingHistory?o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",null,"加载历史消息中...")):o("div",null,this.messages.map(d=>o("div",{id:`message_${d.id}`,key:d.id},o("pcm-chat-message",{botId:this.botId,message:d,userAvatar:this.userAvatar,assistantAvatar:l,showCopyButton:this.showCopyButton,showFeedbackButtons:this.showFeedbackButtons,filePreviewMode:this.filePreviewMode,onFilePreviewRequest:this.handleFilePreviewRequest,onMessageChange:c=>{const p=this.messages.map(u=>u.id===d.id?{...u,...c.detail}:u);this.messages=p}}))),this.currentStreamingMessage&&o("div",{id:`message_${this.currentStreamingMessage.id}`},o("pcm-chat-message",{botId:this.botId,message:this.currentStreamingMessage,userAvatar:this.userAvatar,assistantAvatar:l,showCopyButton:this.showCopyButton,showFeedbackButtons:this.showFeedbackButtons,filePreviewMode:this.filePreviewMode,onFilePreviewRequest:this.handleFilePreviewRequest})),this.messages.length===0&&!this.currentStreamingMessage&&o("div",{class:"empty-state"},o("p",null,"正在准备面试...")))),o("div",{class:"recording-section"},o("div",{class:"recording-container"},this.interviewMode==="text"&&a(),this.interviewMode==="video"&&o("div",{class:"video-container"},o("div",{class:"video-area"},this.showRecordingUI?r():o("div",{class:"video-preview placeholder"},s())),o("div",{class:"recording-controls"},this.showRecordingUI?o("button",{class:"stop-recording-button",onClick:()=>this.stopRecording()},"完成本题回答"):o("div",{class:"waiting-message"},(()=>!this.enableVoice&&this.audioUrl&&!this.isPlayingAudio?o("div",{class:"play-audio-container",onClick:this.handlePlayAudio},o("p",null,o("svg",{viewBox:"0 0 24 24",width:"24",height:"24",fill:"currentColor",style:{verticalAlign:"middle",marginRight:"8px"}},o("path",{d:"M8 5v14l11-7z"})),o("span",{style:{verticalAlign:"middle"}},"播放题目"))):o("button",{class:"stop-recording-button disabled",disabled:!0},"完成回答"))())))))),o("pcm-drawer",{isOpen:this.isDrawerOpen,drawerTitle:this.previewFileName,width:"80%",onClosed:()=>{this.isDrawerOpen=!1,this.previewUrl="",this.previewContent=""}},this.previewContentType==="file"&&this.previewUrl&&o("div",{class:"file-preview-container"},o("iframe",{src:this.previewUrl,frameborder:"0",width:"100%",height:"100%",style:{border:"none",height:"calc(100vh - 120px)"}})),this.previewContentType==="markdown"&&this.previewContent&&o("div",{class:"markdown-preview-container markdown-body",innerHTML:F(this.previewContent)}),this.previewContentType==="text"&&this.previewContent&&o("div",{class:"text-preview-container"},o("pre",null,this.previewContent)))))}static get watchers(){return{token:["handleTokenChange"]}}};De.style=ze+_e;const Me=":host{display:inline-block}.pcm-button{display:inline-flex;align-items:center;justify-content:center;font-weight:400;white-space:nowrap;text-align:center;background-image:none;border:1px solid transparent;cursor:pointer;transition:all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);user-select:none;touch-action:manipulation;height:32px;padding:0 15px;font-size:14px;border-radius:4px;color:rgba(0, 0, 0, 0.85);background-color:white;border-color:#d9d9d9;outline:none}.pcm-button:hover{border-color:#40a9ff;color:#40a9ff}.pcm-button:active{border-color:#096dd9;color:#096dd9}.pcm-button-default{background-color:white;border-color:#d9d9d9;color:rgba(0, 0, 0, 0.85)}.pcm-button-primary{background-color:#1677FF;border-color:#1677FF;color:white}.pcm-button-primary:hover{background-color:#40a9ff;border-color:#40a9ff;color:white}.pcm-button-primary:active{background-color:#096dd9;border-color:#096dd9;color:white}.pcm-button-dashed{background-color:white;border-color:#d9d9d9;border-style:dashed}.pcm-button-text{background-color:transparent;border-color:transparent;color:rgba(0, 0, 0, 0.85);box-shadow:none}.pcm-button-text:hover{background-color:rgba(0, 0, 0, 0.05);border-color:transparent;color:rgba(0, 0, 0, 0.85)}.pcm-button-link{background-color:transparent;border-color:transparent;color:#1677FF;box-shadow:none}.pcm-button-link:hover{color:#40a9ff;background-color:transparent;border-color:transparent}.pcm-button-large{height:40px;padding:0 20px;font-size:16px}.pcm-button-small{height:24px;padding:0 7px;font-size:12px}.pcm-button-circle{min-width:32px;padding:0;border-radius:50%}.pcm-button-large.pcm-button-circle{min-width:40px}.pcm-button-small.pcm-button-circle{min-width:24px}.pcm-button-round{border-radius:40px}.pcm-button-loading{opacity:0.65;cursor:default}.loading-icon{width:14px;height:14px;margin-right:8px;border:2px solid currentColor;border-top-color:transparent;border-radius:50%;animation:loading-spin 1s infinite linear;display:inline-block}@keyframes loading-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.pcm-button-disabled{cursor:not-allowed;opacity:0.65}.pcm-button-disabled:hover,.pcm-button-disabled:active{color:rgba(0, 0, 0, 0.25);background-color:#f5f5f5;border-color:#d9d9d9}.pcm-button-primary.pcm-button-disabled:hover,.pcm-button-primary.pcm-button-disabled:active{background-color:#1677FF;border-color:#1677FF;color:white}.button-icon{margin-right:8px;display:inline-flex;align-items:center}.button-icon img{width:16px;height:16px}.pcm-button-small .button-icon img{width:12px;height:12px}.pcm-button-large .button-icon img{width:18px;height:18px}.pcm-button-block{width:100%;display:flex}",Ae=class{constructor(e){i(this,"type","default");i(this,"size","middle");i(this,"loading",!1);i(this,"disabled",!1);i(this,"icon","https://pub.pincaimao.com/static/common/i_pcm_logo.png");i(this,"shape","default");i(this,"backgroundColor","");i(this,"textColor","");i(this,"borderColor","");i(this,"borderRadius",null);i(this,"width","");i(this,"block",!1);i(this,"borderStyle","solid");S(this,e)}render(){const e={"pcm-button":!0,[`pcm-button-${this.type}`]:!0,[`pcm-button-${this.size}`]:!0,[`pcm-button-${this.shape}`]:!0,"pcm-button-loading":this.loading,"pcm-button-disabled":this.disabled,"pcm-button-block":this.block},t={};return this.backgroundColor&&(t.backgroundColor=this.backgroundColor),this.textColor&&(t.color=this.textColor),this.borderColor&&(t.borderColor=this.borderColor),this.borderRadius!==null&&(t.borderRadius=`${this.borderRadius}px`),this.width&&(t.width=this.width),this.borderStyle&&(t.borderStyle=this.borderStyle),o("button",{key:"f8105a4e08d60af62e77185726b2d67f8378ad2a",class:Object.keys(e).filter(n=>e[n]).join(" "),style:t,disabled:this.disabled,type:"button"},this.loading&&o("span",{key:"713aaf3ed2e05e933e3813128146cad481179055",class:"loading-icon"}),this.icon&&!this.loading&&o("span",{key:"f680ccc5a0481c8c2eddaef84497475426eba8fb",class:"button-icon"},o("img",{key:"888b1a7ba7427a91f4c53732ba4583e3cc68fbe5",src:this.icon,alt:""})),o("slot",{key:"1ee6ce028c10949dd2d0bc761c52b9e01d8fc673"}))}};Ae.style=Me;const Re=`:host{display:block}.loading-container,.error-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px;width:100%;color:#86909C;font-size:14px;text-align:center}.loading-spinner{width:32px;height:32px;border:3px solid #f3f3f3;border-top:3px solid #1677FF;border-radius:50%;margin-bottom:12px;animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.error-icon{width:32px;height:32px;background-color:#FFF0F0;color:#F53F3F;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:20px;font-weight:bold;margin-bottom:12px}.error-message{color:#86909C;max-width:80%}.card-container{border-radius:8px;padding:16px;cursor:pointer;transition:all 0.2s ease;display:flex;flex-direction:column;background-color:#ffffff;border:1px solid #E5E6EB;position:relative;height:100%;min-width:300px}.card-container:hover{transform:translateY(-2px);box-shadow:0 4px 16px rgba(0, 21, 41, 0.08)}.card-header{display:flex;width:100%;padding-bottom:16px;gap:12px}.card-icon{width:64px;height:64px;margin-right:12px;flex-shrink:0;overflow:hidden;border-radius:2px;background-color:#f8f9fa}.card-icon img{width:100%;height:100%;object-fit:contain}.card-info{flex:1;display:flex;flex-direction:column;overflow:hidden}.title-row{display:flex;align-items:center}.title-wrapper{flex:1;margin-right:8px;overflow:hidden}.card-title{font-size:18px;font-weight:700;color:#1D2129;line-height:24px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;width:100%}.chat-tag{display:flex;align-items:center;justify-content:center;height:24px;padding:0 8px;background-color:#F2F7E8;color:#56961F;font-size:12px;border-radius:4px;font-weight:500;flex-shrink:0}.author-row{display:flex;align-items:center;margin-top:8px}.author-avatar{width:16px;height:16px;border-radius:50%;margin-right:4px;background-color:#f8f9fa;overflow:hidden}.author-name{font-size:12px;color:#86909C}.card-description{font-size:12px;color:#86909C;margin-top:8px;line-height:18px;height:54px;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis;white-space:pre-line;word-break:break-word}.card-footer{padding-top:16px;display:flex;justify-content:space-between;align-items:center;border-top:1px solid #E5E6EB}.stats-container{display:flex;align-items:center;flex:1}.stat-item{display:flex;align-items:center;margin-right:8px;color:#86909C}.stat-icon{width:16px;height:16px;margin-right:4px;background-repeat:no-repeat;background-position:center;background-size:contain}.user-icon{background-image:url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' focusable='false' aria-hidden='true'%3E%3Cpath d='M12 14c-3.3 0-6-2.7-6-6s2.7-6 6-6 6 2.7 6 6-2.7 6-6 6Zm0-10C9.8 4 8 5.8 8 8s1.8 4 4 4 4-1.8 4-4-1.8-4-4-4Z' fill='currentColor'%3E%3C/path%3E%3Cpath d='M21.9 20.6c-.1-.2-2.3-5.6-9.9-5.6-7.6 0-9.8 5.4-9.9 5.6-.2.5 0 1.1.6 ******* 1.1 0 1.3-.5 0-.2 1.8-4.4 8-4.4s8 4.2 8.1 4.4c.******* *******-.2.7-.8.5-1.3Z' fill='currentColor'%3E%3C/path%3E%3C/svg%3E")}.star-icon{background-image:url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' focusable='false' aria-hidden='true'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12 5.03 9.57 9.1c-.17.3-.46.5-.8.55l-4.3.79 3 3.46c.**********.28.93l-.62 4.46 4.41-1.9c.3-.12.63-.12.92 0l4.4 1.9-.6-4.46c-.05-.34.05-.67.27-.93l3-3.46-4.3-.79a1.17 1.17 0 0 1-.8-.55L12 5.03Zm-1-2.46a1.16 1.16 0 0 1 2 0l3.03 5.07 5.51 1c.89.17 1.26 1.24.68 1.92l-3.8 4.4.77 5.71c.13.9-.78 1.6-1.61 1.23L12 19.5l-5.58 2.4a1.17 1.17 0 0 1-1.61-1.23l.78-5.7-3.8-4.4a1.17 1.17 0 0 1 .67-1.92l5.51-1L11 2.56Z' fill='currentColor'%3E%3C/path%3E%3C/svg%3E")}.video-icon{background-image:url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' focusable='false' aria-hidden='true'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M4 2a2 2 0 0 0-2 2v16c0 1.1.9 2 2 2h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H4Zm0 2h16v16H4V4Zm5.5 3.13A1 1 0 0 0 8 8v8a1 1 0 0 0 1.5.87l7-4a1 1 0 0 0 0-1.74l-7-4ZM13.98 12 10 14.28V9.72L13.98 12Z' fill='currentColor'%3E%3C/path%3E%3C/svg%3E")}.stat-value{font-size:12px}.use-button{font-size:12px;color:#86909C;cursor:pointer;white-space:nowrap}.use-button:hover{color:#1677FF}@media screen and (max-width: 768px){.card-container{padding:12px}.card-header{gap:8px;padding-bottom:12px}.card-icon{width:48px;height:48px}.card-title{font-size:16px;line-height:20px}.card-description{font-size:12px;line-height:16px;height:48px}}`,je=class{constructor(e){i(this,"token");i(this,"cardTitle","");i(this,"description","");i(this,"iconUrl","");i(this,"author","");i(this,"authorAvatarUrl","");i(this,"showChatTag",!1);i(this,"customChatTag","");i(this,"useButtonText","立即使用");i(this,"botId","");i(this,"botData",null);i(this,"loading",!1);i(this,"error","");i(this,"tokenInvalid");i(this,"tokenInvalidListener");S(this,e),this.tokenInvalid=m(this,"tokenInvalid")}watchBotIdHandler(e){e&&this.fetchBotData()}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}componentWillLoad(){this.token&&k.setToken(this.token),this.botId&&this.fetchBotData(),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener)}async fetchBotData(){if(this.botId){this.loading=!0,this.error="";try{const e=await M({url:`/sdk/v1/agent/${this.botId}/info`,method:"GET"});if(e.success&&e.data)this.botData=e.data;else throw new Error(e.message||"获取智能体信息失败")}catch(e){this.error=e.message||"获取智能体信息失败",console.error("获取智能体信息失败:",e)}finally{this.loading=!1}}}render(){var u,g,h,f,v,C,E,T,D,P,H;const e=this.cardTitle||((u=this.botData)==null?void 0:u.title)||"",t=this.description||((g=this.botData)==null?void 0:g.description)||"",n=((h=this.botData)==null?void 0:h.icon)||((f=this.botData)==null?void 0:f.iconUrl)||((v=this.botData)==null?void 0:v.logo)||"",r=this.iconUrl||n,s=this.author||((C=this.botData)==null?void 0:C.author_name)||"",a=this.authorAvatarUrl||((E=this.botData)==null?void 0:E.author_avatar)||"",l=this.customChatTag||((T=this.botData)==null?void 0:T.flag)||"",d=((D=this.botData)==null?void 0:D.use_count)||0,c=((P=this.botData)==null?void 0:P.follow_count)||0,p=((H=this.botData)==null?void 0:H.view_count)||0;return o("div",{key:"686774ebe2a5e2942c7e41d73a9fd8ae8e427f5d",class:"card-container"},this.loading?o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("div",null,"加载中...")):this.error?o("div",{class:"error-container"},o("div",{class:"error-icon"},"!"),o("div",{class:"error-message"},this.error)):o("div",null,o("div",{class:"card-header"},r&&o("div",{class:"card-icon"},o("img",{src:r,alt:e})),o("div",{class:"card-info"},o("div",{class:"title-row"},o("div",{class:"title-wrapper"},o("div",{class:"card-title"},e)),this.showChatTag&&l&&o("div",{class:"chat-tag"},l)),s&&o("div",{class:"author-row"},a&&o("img",{class:"author-avatar",src:a,alt:s}),o("div",{class:"author-name"},s)),t&&o("div",{class:"card-description"},t))),o("div",{class:"card-footer"},o("div",{class:"stats-container"},o("div",{class:"stat-item"},o("span",{class:"stat-icon user-icon"}),o("div",{class:"stat-value"},d)),o("div",{class:"stat-item"},o("span",{class:"stat-icon star-icon"}),o("div",{class:"stat-value"},c)),o("div",{class:"stat-item"},o("span",{class:"stat-icon video-icon"}),o("div",{class:"stat-value"},p))),o("div",{class:"use-button"},this.useButtonText||"立即使用"))))}static get watchers(){return{botId:["watchBotIdHandler"],token:["handleTokenChange"]}}};je.style=Re;function Be(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var X,le;function Le(){if(le)return X;le=1;function e({interruptPatterns:r=[],skipEmptyRows:s=!0}={}){return{extensions:[{name:"spanTable",level:"block",start(a){var l;return(l=a.match(/\n *([^\n ].*\|.*)\n/m))==null?void 0:l.index},tokenizer(a,l){var g;let d="^ *([^\\n ].*\\|.*\\n(?: *[^\\s].*\\n)*?) {0,3}(?:\\| *)?(:?-+(?: *(?:100|[1-9][0-9]?%) *-+)?:? *(?:\\| *:?-+(?: *(?:100|[1-9][0-9]?%) *-+)?:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n| {0,3}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)| {0,3}#{1,6}(?:\\s|$)| {0,3}>| {4}[^\\n]| {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n| {0,3}(?:[*+-]|1[.)]) |<\\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?: +|\\n|\\/?>)|<(?:script|pre|style|textarea|!--)endRegex).*(?:\\n|$))*)\\n*|$)";d=d.replace("endRegex",r.map(h=>`|(?:${h})`).join(""));const c=/ *(?:100|[1-9][0-9]?%) */g,u=new RegExp(d).exec(a);if(u){const h={type:"spanTable",header:u[1].replace(/\n$/,"").split(`
`),align:u[2].replace(c,"").replace(/^ *|\| *$/g,"").split(/ *\| */),rows:(g=u[3])!=null&&g.trim()?u[3].replace(/\n[ \t]*$/,"").split(`
`):[],width:u[2].replace(/:/g,"").replace(/-+| /g,"").split("|")};h.header[0]=n(h.header[0]);const f=h.header[0].reduce((v,C)=>v+C.colspan,0);if(f===h.align.length){h.raw=u[0];let v,C,E,T,D=h.align.length;for(v=0;v<D;v++)/^ *-+: *$/.test(h.align[v])?h.align[v]="right":/^ *:-+: *$/.test(h.align[v])?h.align[v]="center":/^ *:-+ *$/.test(h.align[v])?h.align[v]="left":h.align[v]=null;for(D=h.header.length,v=1;v<D;v++)h.header[v]=n(h.header[v],f,h.header[v-1],s);for(D=h.rows.length,v=0;v<D;v++)h.rows[v]=n(h.rows[v],f,h.rows[v-1],s);for(D=h.header.length,C=0;C<D;C++)for(T=h.header[C],E=0;E<T.length;E++)T[E].tokens=[],this.lexer.inline(T[E].text,T[E].tokens);for(D=h.rows.length,C=0;C<D;C++)for(T=h.rows[C],E=0;E<T.length;E++)T[E].tokens=[],this.lexer.inline(T[E].text,T[E].tokens);return h}}},renderer(a){let l,d,c,p,u,g,h="<table>";for(h+="<thead>",l=0;l<a.header.length;l++){c=a.header[l];let f=0;for(h+="<tr>",d=0;d<c.length;d++)p=c[d],g=this.parser.parseInline(p.tokens),h+=t(g,p,"th",a.align[f],a.width[f]),f+=p.colspan;h+="</tr>"}if(h+="</thead>",a.rows.length){for(h+="<tbody>",l=0;l<a.rows.length;l++)if(c=a.rows[l],u=0,!c[0].emptyRow){for(h+="<tr>",d=0;d<c.length;d++)p=c[d],g=this.parser.parseInline(p.tokens),h+=t(g,p,"td",a.align[u],a.width[u]),u+=p.colspan;h+="</tr>"}h+="</tbody>"}return h+="</table>",h}}]}}const t=(r,s,a,l,d)=>s.rowspan?`${`<${a}${s.colspan>1?` colspan=${s.colspan}`:""}${s.rowspan>1?` rowspan=${s.rowspan}`:""}${l?` align=${l}`:""}${d?` width=${d}`:""}>`+r}</${a}>
`:"",n=(r,s,a=[],l)=>{var v,C;const d=[...r.trim().matchAll(/(?:[^|\\]|\\.?)+(?:\|+|$)/g)].map(E=>E[0]);(v=d[0])!=null&&v.trim()||d.shift(),(C=d[d.length-1])!=null&&C.trim()||d.pop();let c=0,p,u,g,h,f;for(p=0;p<d.length;p++){if(g=d[p].split(/\|+$/)[0],d[p]={rowspan:1,colspan:Math.max(d[p].length-g.length,1),text:g.trim().replace(/\\\|/g,"|")},g.slice(-1)==="^"&&a.length)for(f=0,u=0;u<a.length;u++){if(h=a[u],f===c&&h.colspan===d[p].colspan){d[p].rowSpanTarget=h.rowSpanTarget??h,d[p].rowSpanTarget.text+=` ${d[p].text.slice(0,-1)}`,d[p].rowSpanTarget.rowspan+=1,d[p].rowspan=0;break}if(f+=h.colspan,f>c)break}c+=d[p].colspan}if(d.length>0&&l&&d.length===d.filter(E=>E.rowspan===0).length)for(d[0].emptyRow=!0,p=0;p<d.length;p++)d[p].rowSpanTarget.rowspan-=1;if(c>s)d.splice(s);else for(;c<s;)d.push({rowspan:1,colspan:1,text:""}),c+=1;return d};return X=e,X}var Ue=Le(),$e=Be(Ue);const Pe=`.markdown-body{--base-size-4:4px;--base-size-8:8px;--base-size-16:16px;--base-size-24:24px;--base-size-40:40px;--base-text-weight-normal:400;--base-text-weight-medium:500;--base-text-weight-semibold:600;--fontStack-monospace:ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;--fgColor-accent:Highlight}@media (prefers-color-scheme: dark){.markdown-body,[data-theme="dark"]{color-scheme:dark;--focus-outlineColor:#1f6feb;--fgColor-default:#f0f6fc;--fgColor-muted:#9198a1;--fgColor-accent:#4493f8;--fgColor-success:#3fb950;--fgColor-attention:#d29922;--fgColor-danger:#f85149;--fgColor-done:#ab7df8;--bgColor-default:#0d1117;--bgColor-muted:#151b23;--bgColor-neutral-muted:#656c7633;--bgColor-attention-muted:#bb800926;--borderColor-default:#3d444d;--borderColor-muted:#3d444db3;--borderColor-neutral-muted:#3d444db3;--borderColor-accent-emphasis:#1f6feb;--borderColor-success-emphasis:#238636;--borderColor-attention-emphasis:#9e6a03;--borderColor-danger-emphasis:#da3633;--borderColor-done-emphasis:#8957e5;--color-prettylights-syntax-comment:#9198a1;--color-prettylights-syntax-constant:#79c0ff;--color-prettylights-syntax-constant-other-reference-link:#a5d6ff;--color-prettylights-syntax-entity:#d2a8ff;--color-prettylights-syntax-storage-modifier-import:#f0f6fc;--color-prettylights-syntax-entity-tag:#7ee787;--color-prettylights-syntax-keyword:#ff7b72;--color-prettylights-syntax-string:#a5d6ff;--color-prettylights-syntax-variable:#ffa657;--color-prettylights-syntax-brackethighlighter-unmatched:#f85149;--color-prettylights-syntax-brackethighlighter-angle:#9198a1;--color-prettylights-syntax-invalid-illegal-text:#f0f6fc;--color-prettylights-syntax-invalid-illegal-bg:#8e1519;--color-prettylights-syntax-carriage-return-text:#f0f6fc;--color-prettylights-syntax-carriage-return-bg:#b62324;--color-prettylights-syntax-string-regexp:#7ee787;--color-prettylights-syntax-markup-list:#f2cc60;--color-prettylights-syntax-markup-heading:#1f6feb;--color-prettylights-syntax-markup-italic:#f0f6fc;--color-prettylights-syntax-markup-bold:#f0f6fc;--color-prettylights-syntax-markup-deleted-text:#ffdcd7;--color-prettylights-syntax-markup-deleted-bg:#67060c;--color-prettylights-syntax-markup-inserted-text:#aff5b4;--color-prettylights-syntax-markup-inserted-bg:#033a16;--color-prettylights-syntax-markup-changed-text:#ffdfb6;--color-prettylights-syntax-markup-changed-bg:#5a1e02;--color-prettylights-syntax-markup-ignored-text:#f0f6fc;--color-prettylights-syntax-markup-ignored-bg:#1158c7;--color-prettylights-syntax-meta-diff-range:#d2a8ff;--color-prettylights-syntax-sublimelinter-gutter-mark:#3d444d}}@media (prefers-color-scheme: light){.markdown-body,[data-theme="light"]{color-scheme:light;--focus-outlineColor:#0969da;--fgColor-default:#1f2328;--fgColor-muted:#59636e;--fgColor-accent:#0969da;--fgColor-success:#1a7f37;--fgColor-attention:#9a6700;--fgColor-danger:#d1242f;--fgColor-done:#8250df;--bgColor-default:#ffffff;--bgColor-muted:#f6f8fa;--bgColor-neutral-muted:#818b981f;--bgColor-attention-muted:#fff8c5;--borderColor-default:#d1d9e0;--borderColor-muted:#d1d9e0b3;--borderColor-neutral-muted:#d1d9e0b3;--borderColor-accent-emphasis:#0969da;--borderColor-success-emphasis:#1a7f37;--borderColor-attention-emphasis:#9a6700;--borderColor-danger-emphasis:#cf222e;--borderColor-done-emphasis:#8250df;--color-prettylights-syntax-comment:#59636e;--color-prettylights-syntax-constant:#0550ae;--color-prettylights-syntax-constant-other-reference-link:#0a3069;--color-prettylights-syntax-entity:#6639ba;--color-prettylights-syntax-storage-modifier-import:#1f2328;--color-prettylights-syntax-entity-tag:#0550ae;--color-prettylights-syntax-keyword:#cf222e;--color-prettylights-syntax-string:#0a3069;--color-prettylights-syntax-variable:#953800;--color-prettylights-syntax-brackethighlighter-unmatched:#82071e;--color-prettylights-syntax-brackethighlighter-angle:#59636e;--color-prettylights-syntax-invalid-illegal-text:#f6f8fa;--color-prettylights-syntax-invalid-illegal-bg:#82071e;--color-prettylights-syntax-carriage-return-text:#f6f8fa;--color-prettylights-syntax-carriage-return-bg:#cf222e;--color-prettylights-syntax-string-regexp:#116329;--color-prettylights-syntax-markup-list:#3b2300;--color-prettylights-syntax-markup-heading:#0550ae;--color-prettylights-syntax-markup-italic:#1f2328;--color-prettylights-syntax-markup-bold:#1f2328;--color-prettylights-syntax-markup-deleted-text:#82071e;--color-prettylights-syntax-markup-deleted-bg:#ffebe9;--color-prettylights-syntax-markup-inserted-text:#116329;--color-prettylights-syntax-markup-inserted-bg:#dafbe1;--color-prettylights-syntax-markup-changed-text:#953800;--color-prettylights-syntax-markup-changed-bg:#ffd8b5;--color-prettylights-syntax-markup-ignored-text:#d1d9e0;--color-prettylights-syntax-markup-ignored-bg:#0550ae;--color-prettylights-syntax-meta-diff-range:#8250df;--color-prettylights-syntax-sublimelinter-gutter-mark:#818b98}}.markdown-body{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;margin:0;font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";font-size:16px;line-height:1.5;word-wrap:break-word}.markdown-body .octicon{display:inline-block;fill:currentColor;vertical-align:text-bottom}.markdown-body h1:hover .anchor .octicon-link:before,.markdown-body h2:hover .anchor .octicon-link:before,.markdown-body h3:hover .anchor .octicon-link:before,.markdown-body h4:hover .anchor .octicon-link:before,.markdown-body h5:hover .anchor .octicon-link:before,.markdown-body h6:hover .anchor .octicon-link:before{width:16px;height:16px;content:' ';display:inline-block;background-color:currentColor;-webkit-mask-image:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");mask-image:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>")}.markdown-body details,.markdown-body figcaption,.markdown-body figure{display:block}.markdown-body summary{display:list-item}.markdown-body [hidden]{display:none !important}.markdown-body a{background-color:transparent;color:var(--fgColor-accent);text-decoration:none}.markdown-body abbr[title]{border-bottom:none;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}.markdown-body b,.markdown-body strong{font-weight:var(--base-text-weight-semibold, 600)}.markdown-body dfn{font-style:italic}.markdown-body h1{margin:.67em 0;font-weight:var(--base-text-weight-semibold, 600);padding-bottom:.3em;font-size:1.5em;border-bottom:1px solid var(--borderColor-muted)}.markdown-body mark{background-color:var(--bgColor-attention-muted);color:var(--fgColor-default)}.markdown-body small{font-size:90%}.markdown-body sub,.markdown-body sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}.markdown-body sub{bottom:-0.25em}.markdown-body sup{top:-0.5em}.markdown-body img{border-style:none;max-width:100%;box-sizing:content-box}.markdown-body code,.markdown-body kbd,.markdown-body pre,.markdown-body samp{font-family:monospace;font-size:1em}.markdown-body figure{margin:1em var(--base-size-40)}.markdown-body hr{box-sizing:content-box;overflow:hidden;background:transparent;border-bottom:1px solid var(--borderColor-muted);height:.25em;padding:0;margin:var(--base-size-24) 0;background-color:var(--borderColor-default);border:0}.markdown-body input{font:inherit;margin:0;overflow:visible;font-family:inherit;font-size:inherit;line-height:inherit}.markdown-body [type=button],.markdown-body [type=reset],.markdown-body [type=submit]{-webkit-appearance:button;appearance:button}.markdown-body [type=checkbox],.markdown-body [type=radio]{box-sizing:border-box;padding:0}.markdown-body [type=number]::-webkit-inner-spin-button,.markdown-body [type=number]::-webkit-outer-spin-button{height:auto}.markdown-body [type=search]::-webkit-search-cancel-button,.markdown-body [type=search]::-webkit-search-decoration{-webkit-appearance:none;appearance:none}.markdown-body ::-webkit-input-placeholder{color:inherit;opacity:.54}.markdown-body ::-webkit-file-upload-button{-webkit-appearance:button;appearance:button;font:inherit}.markdown-body a:hover{text-decoration:underline}.markdown-body ::placeholder{color:var(--fgColor-muted);opacity:1}.markdown-body hr::before{display:table;content:""}.markdown-body hr::after{display:table;clear:both;content:""}.markdown-body table{border-spacing:0;border-collapse:collapse;display:block;width:max-content;max-width:100%;overflow:auto;font-variant:tabular-nums}.markdown-body td,.markdown-body th{padding:0}.markdown-body details summary{cursor:pointer}.markdown-body a:focus,.markdown-body [role=button]:focus,.markdown-body input[type=radio]:focus,.markdown-body input[type=checkbox]:focus{outline:2px solid var(--focus-outlineColor);outline-offset:-2px;box-shadow:none}.markdown-body a:focus:not(:focus-visible),.markdown-body [role=button]:focus:not(:focus-visible),.markdown-body input[type=radio]:focus:not(:focus-visible),.markdown-body input[type=checkbox]:focus:not(:focus-visible){outline:solid 1px transparent}.markdown-body a:focus-visible,.markdown-body [role=button]:focus-visible,.markdown-body input[type=radio]:focus-visible,.markdown-body input[type=checkbox]:focus-visible{outline:2px solid var(--focus-outlineColor);outline-offset:-2px;box-shadow:none}.markdown-body a:not([class]):focus,.markdown-body a:not([class]):focus-visible,.markdown-body input[type=radio]:focus,.markdown-body input[type=radio]:focus-visible,.markdown-body input[type=checkbox]:focus,.markdown-body input[type=checkbox]:focus-visible{outline-offset:0}.markdown-body kbd{display:inline-block;padding:var(--base-size-4);font:11px var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);line-height:10px;color:var(--fgColor-default);vertical-align:middle;background-color:var(--bgColor-muted);border:solid 1px var(--borderColor-neutral-muted);border-bottom-color:var(--borderColor-neutral-muted);border-radius:6px;box-shadow:inset 0 -1px 0 var(--borderColor-neutral-muted)}.markdown-body h1,.markdown-body h2,.markdown-body h3,.markdown-body h4,.markdown-body h5,.markdown-body h6{margin-top:var(--base-size-24);margin-bottom:var(--base-size-16);font-weight:var(--base-text-weight-semibold, 600);line-height:1.25}.markdown-body h2{font-weight:var(--base-text-weight-semibold, 600);padding-bottom:.3em;font-size:1.2em;border-bottom:1px solid var(--borderColor-muted)}.markdown-body h3{font-weight:var(--base-text-weight-semibold, 600);font-size:1.05em}.markdown-body h4{font-weight:var(--base-text-weight-semibold, 600);font-size:0.875em}.markdown-body h5{font-weight:var(--base-text-weight-semibold, 600);font-size:.85em}.markdown-body h6{font-weight:var(--base-text-weight-semibold, 600);font-size:.80em;color:var(--fgColor-muted)}.markdown-body p{margin-top:0;margin-bottom:10px}.markdown-body blockquote{margin:0;padding:0 1em;color:var(--fgColor-muted);border-left:.25em solid var(--borderColor-default)}.markdown-body ul,.markdown-body ol{margin-top:0;margin-bottom:0;padding-left:2em}.markdown-body ol ol,.markdown-body ul ol{list-style-type:lower-roman}.markdown-body ul ul ol,.markdown-body ul ol ol,.markdown-body ol ul ol,.markdown-body ol ol ol{list-style-type:lower-alpha}.markdown-body dd{margin-left:0}.markdown-body tt,.markdown-body code,.markdown-body samp{font-family:var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);font-size:12px}.markdown-body pre{margin-top:0;margin-bottom:0;font-family:var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);font-size:12px;word-wrap:normal}.markdown-body .octicon{display:inline-block;overflow:visible !important;vertical-align:text-bottom;fill:currentColor}.markdown-body input::-webkit-outer-spin-button,.markdown-body input::-webkit-inner-spin-button{margin:0;appearance:none}.markdown-body .mr-2{margin-right:var(--base-size-8, 8px) !important}.markdown-body::before{display:table;content:""}.markdown-body::after{display:table;clear:both;content:""}.markdown-body>*:first-child{margin-top:0 !important}.markdown-body>*:last-child{margin-bottom:0 !important}.markdown-body a:not([href]){color:inherit;text-decoration:none}.markdown-body .absent{color:var(--fgColor-danger)}.markdown-body .anchor{float:left;padding-right:var(--base-size-4);margin-left:-20px;line-height:1}.markdown-body .anchor:focus{outline:none}.markdown-body p,.markdown-body blockquote,.markdown-body ul,.markdown-body ol,.markdown-body dl,.markdown-body table,.markdown-body pre,.markdown-body details{margin-top:0;margin-bottom:var(--base-size-16);font-size:16px;font-weight:400}.markdown-body blockquote>:first-child{margin-top:0}.markdown-body blockquote>:last-child{margin-bottom:0}.markdown-body h1 .octicon-link,.markdown-body h2 .octicon-link,.markdown-body h3 .octicon-link,.markdown-body h4 .octicon-link,.markdown-body h5 .octicon-link,.markdown-body h6 .octicon-link{color:var(--fgColor-default);vertical-align:middle;visibility:hidden}.markdown-body h1:hover .anchor,.markdown-body h2:hover .anchor,.markdown-body h3:hover .anchor,.markdown-body h4:hover .anchor,.markdown-body h5:hover .anchor,.markdown-body h6:hover .anchor{text-decoration:none}.markdown-body h1:hover .anchor .octicon-link,.markdown-body h2:hover .anchor .octicon-link,.markdown-body h3:hover .anchor .octicon-link,.markdown-body h4:hover .anchor .octicon-link,.markdown-body h5:hover .anchor .octicon-link,.markdown-body h6:hover .anchor .octicon-link{visibility:visible}.markdown-body h1 tt,.markdown-body h1 code,.markdown-body h2 tt,.markdown-body h2 code,.markdown-body h3 tt,.markdown-body h3 code,.markdown-body h4 tt,.markdown-body h4 code,.markdown-body h5 tt,.markdown-body h5 code,.markdown-body h6 tt,.markdown-body h6 code{padding:0 .2em;font-size:inherit}.markdown-body summary h1,.markdown-body summary h2,.markdown-body summary h3,.markdown-body summary h4,.markdown-body summary h5,.markdown-body summary h6{display:inline-block}.markdown-body summary h1 .anchor,.markdown-body summary h2 .anchor,.markdown-body summary h3 .anchor,.markdown-body summary h4 .anchor,.markdown-body summary h5 .anchor,.markdown-body summary h6 .anchor{margin-left:-40px}.markdown-body summary h1,.markdown-body summary h2{padding-bottom:0;border-bottom:0}.markdown-body ul.no-list,.markdown-body ol.no-list{padding:0;list-style-type:none}.markdown-body ol[type="a s"]{list-style-type:lower-alpha}.markdown-body ol[type="A s"]{list-style-type:upper-alpha}.markdown-body ol[type="i s"]{list-style-type:lower-roman}.markdown-body ol[type="I s"]{list-style-type:upper-roman}.markdown-body ol[type="1"]{list-style-type:decimal}.markdown-body div>ol:not([type]){list-style-type:decimal}.markdown-body ul ul,.markdown-body ul ol,.markdown-body ol ol,.markdown-body ol ul{margin-top:0;margin-bottom:0}.markdown-body li>p{margin-top:var(--base-size-16)}.markdown-body li+li{margin-top:.25em}.markdown-body dl{padding:0}.markdown-body dl dt{padding:0;margin-top:var(--base-size-16);font-size:1em;font-style:italic;font-weight:var(--base-text-weight-semibold, 600)}.markdown-body dl dd{padding:0 var(--base-size-16);margin-bottom:var(--base-size-16)}.markdown-body table th{font-weight:var(--base-text-weight-semibold, 600)}.markdown-body table th,.markdown-body table td{padding:6px 13px;border:1px solid var(--borderColor-default)}.markdown-body table td>:last-child{margin-bottom:0}.markdown-body table tr{background-color:var(--bgColor-default);border-top:1px solid var(--borderColor-muted)}.markdown-body table tr:nth-child(2n){background-color:var(--bgColor-muted)}.markdown-body table img{background-color:transparent}.markdown-body img[align=right]{padding-left:20px}.markdown-body img[align=left]{padding-right:20px}.markdown-body .emoji{max-width:none;vertical-align:text-top;background-color:transparent}.markdown-body span.frame{display:block;overflow:hidden}.markdown-body span.frame>span{display:block;float:left;width:auto;padding:7px;margin:13px 0 0;overflow:hidden;border:1px solid var(--borderColor-default)}.markdown-body span.frame span img{display:block;float:left}.markdown-body span.frame span span{display:block;padding:5px 0 0;clear:both;color:var(--fgColor-default)}.markdown-body span.align-center{display:block;overflow:hidden;clear:both}.markdown-body span.align-center>span{display:block;margin:13px auto 0;overflow:hidden;text-align:center}.markdown-body span.align-center span img{margin:0 auto;text-align:center}.markdown-body span.align-right{display:block;overflow:hidden;clear:both}.markdown-body span.align-right>span{display:block;margin:13px 0 0;overflow:hidden;text-align:right}.markdown-body span.align-right span img{margin:0;text-align:right}.markdown-body span.float-left{display:block;float:left;margin-right:13px;overflow:hidden}.markdown-body span.float-left span{margin:13px 0 0}.markdown-body span.float-right{display:block;float:right;margin-left:13px;overflow:hidden}.markdown-body span.float-right>span{display:block;margin:13px auto 0;overflow:hidden;text-align:right}.markdown-body code,.markdown-body tt{padding:.2em .4em;margin:0;font-size:85%;white-space:break-spaces;background-color:var(--bgColor-neutral-muted);border-radius:6px}.markdown-body code br,.markdown-body tt br{display:none}.markdown-body del code{text-decoration:inherit}.markdown-body samp{font-size:85%}.markdown-body pre code{font-size:100%}.markdown-body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:transparent;border:0}.markdown-body .highlight{margin-bottom:var(--base-size-16)}.markdown-body .highlight pre{margin-bottom:0;word-break:normal}.markdown-body .highlight pre,.markdown-body pre{padding:var(--base-size-16);overflow:auto;font-size:85%;line-height:1.45;color:var(--fgColor-default);background-color:var(--bgColor-muted);border-radius:6px}.markdown-body pre code,.markdown-body pre tt{display:inline;max-width:auto;padding:0;margin:0;overflow:visible;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}.markdown-body .csv-data td,.markdown-body .csv-data th{padding:5px;overflow:hidden;font-size:12px;line-height:1;text-align:left;white-space:nowrap}.markdown-body .csv-data .blob-num{padding:10px var(--base-size-8) 9px;text-align:right;background:var(--bgColor-default);border:0}.markdown-body .csv-data tr{border-top:0}.markdown-body .csv-data th{font-weight:var(--base-text-weight-semibold, 600);background:var(--bgColor-muted);border-top:0}.markdown-body [data-footnote-ref]::before{content:"["}.markdown-body [data-footnote-ref]::after{content:"]"}.markdown-body .footnotes{font-size:12px;color:var(--fgColor-muted);border-top:1px solid var(--borderColor-default)}.markdown-body .footnotes ol{padding-left:var(--base-size-16)}.markdown-body .footnotes ol ul{display:inline-block;padding-left:var(--base-size-16);margin-top:var(--base-size-16)}.markdown-body .footnotes li{position:relative}.markdown-body .footnotes li:target::before{position:absolute;top:calc(var(--base-size-8)*-1);right:calc(var(--base-size-8)*-1);bottom:calc(var(--base-size-8)*-1);left:calc(var(--base-size-24)*-1);pointer-events:none;content:"";border:2px solid var(--borderColor-accent-emphasis);border-radius:6px}.markdown-body .footnotes li:target{color:var(--fgColor-default)}.markdown-body .footnotes .data-footnote-backref g-emoji{font-family:monospace}.markdown-body body:has(:modal){padding-right:var(--dialog-scrollgutter) !important}.markdown-body .pl-c{color:var(--color-prettylights-syntax-comment)}.markdown-body .pl-c1,.markdown-body .pl-s .pl-v{color:var(--color-prettylights-syntax-constant)}.markdown-body .pl-e,.markdown-body .pl-en{color:var(--color-prettylights-syntax-entity)}.markdown-body .pl-smi,.markdown-body .pl-s .pl-s1{color:var(--color-prettylights-syntax-storage-modifier-import)}.markdown-body .pl-ent{color:var(--color-prettylights-syntax-entity-tag)}.markdown-body .pl-k{color:var(--color-prettylights-syntax-keyword)}.markdown-body .pl-s,.markdown-body .pl-pds,.markdown-body .pl-s .pl-pse .pl-s1,.markdown-body .pl-sr,.markdown-body .pl-sr .pl-cce,.markdown-body .pl-sr .pl-sre,.markdown-body .pl-sr .pl-sra{color:var(--color-prettylights-syntax-string)}.markdown-body .pl-v,.markdown-body .pl-smw{color:var(--color-prettylights-syntax-variable)}.markdown-body .pl-bu{color:var(--color-prettylights-syntax-brackethighlighter-unmatched)}.markdown-body .pl-ii{color:var(--color-prettylights-syntax-invalid-illegal-text);background-color:var(--color-prettylights-syntax-invalid-illegal-bg)}.markdown-body .pl-c2{color:var(--color-prettylights-syntax-carriage-return-text);background-color:var(--color-prettylights-syntax-carriage-return-bg)}.markdown-body .pl-sr .pl-cce{font-weight:bold;color:var(--color-prettylights-syntax-string-regexp)}.markdown-body .pl-ml{color:var(--color-prettylights-syntax-markup-list)}.markdown-body .pl-mh,.markdown-body .pl-mh .pl-en,.markdown-body .pl-ms{font-weight:bold;color:var(--color-prettylights-syntax-markup-heading)}.markdown-body .pl-mi{font-style:italic;color:var(--color-prettylights-syntax-markup-italic)}.markdown-body .pl-mb{font-weight:bold;color:var(--color-prettylights-syntax-markup-bold)}.markdown-body .pl-md{color:var(--color-prettylights-syntax-markup-deleted-text);background-color:var(--color-prettylights-syntax-markup-deleted-bg)}.markdown-body .pl-mi1{color:var(--color-prettylights-syntax-markup-inserted-text);background-color:var(--color-prettylights-syntax-markup-inserted-bg)}.markdown-body .pl-mc{color:var(--color-prettylights-syntax-markup-changed-text);background-color:var(--color-prettylights-syntax-markup-changed-bg)}.markdown-body .pl-mi2{color:var(--color-prettylights-syntax-markup-ignored-text);background-color:var(--color-prettylights-syntax-markup-ignored-bg)}.markdown-body .pl-mdr{font-weight:bold;color:var(--color-prettylights-syntax-meta-diff-range)}.markdown-body .pl-ba{color:var(--color-prettylights-syntax-brackethighlighter-angle)}.markdown-body .pl-sg{color:var(--color-prettylights-syntax-sublimelinter-gutter-mark)}.markdown-body .pl-corl{text-decoration:underline;color:var(--color-prettylights-syntax-constant-other-reference-link)}.markdown-body [role=button]:focus:not(:focus-visible),.markdown-body [role=tabpanel][tabindex="0"]:focus:not(:focus-visible),.markdown-body button:focus:not(:focus-visible),.markdown-body summary:focus:not(:focus-visible),.markdown-body a:focus:not(:focus-visible){outline:none;box-shadow:none}.markdown-body [tabindex="0"]:focus:not(:focus-visible),.markdown-body details-dialog:focus:not(:focus-visible){outline:none}.markdown-body g-emoji{display:inline-block;min-width:1ch;font-family:"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";font-size:1em;font-style:normal !important;font-weight:var(--base-text-weight-normal, 400);line-height:1;vertical-align:-0.075em}.markdown-body g-emoji img{width:1em;height:1em}.markdown-body .task-list-item{list-style-type:none}.markdown-body .task-list-item label{font-weight:var(--base-text-weight-normal, 400)}.markdown-body .task-list-item.enabled label{cursor:pointer}.markdown-body .task-list-item+.task-list-item{margin-top:var(--base-size-4)}.markdown-body .task-list-item .handle{display:none}.markdown-body .task-list-item-checkbox{margin:0 .2em .25em -1.4em;vertical-align:middle}.markdown-body ul:dir(rtl) .task-list-item-checkbox{margin:0 -1.6em .25em .2em}.markdown-body ol:dir(rtl) .task-list-item-checkbox{margin:0 -1.6em .25em .2em}.markdown-body .contains-task-list:hover .task-list-item-convert-container,.markdown-body .contains-task-list:focus-within .task-list-item-convert-container{display:block;width:auto;height:24px;overflow:visible;clip:auto}.markdown-body ::-webkit-calendar-picker-indicator{filter:invert(50%)}.markdown-body .markdown-alert{padding:var(--base-size-8) var(--base-size-16);margin-bottom:var(--base-size-16);color:inherit;border-left:.25em solid var(--borderColor-default)}.markdown-body .markdown-alert>:first-child{margin-top:0}.markdown-body .markdown-alert>:last-child{margin-bottom:0}.markdown-body .markdown-alert .markdown-alert-title{display:flex;font-weight:var(--base-text-weight-medium, 500);align-items:center;line-height:1}.markdown-body .markdown-alert.markdown-alert-note{border-left-color:var(--borderColor-accent-emphasis)}.markdown-body .markdown-alert.markdown-alert-note .markdown-alert-title{color:var(--fgColor-accent)}.markdown-body .markdown-alert.markdown-alert-important{border-left-color:var(--borderColor-done-emphasis)}.markdown-body .markdown-alert.markdown-alert-important .markdown-alert-title{color:var(--fgColor-done)}.markdown-body .markdown-alert.markdown-alert-warning{border-left-color:var(--borderColor-attention-emphasis)}.markdown-body .markdown-alert.markdown-alert-warning .markdown-alert-title{color:var(--fgColor-attention)}.markdown-body .markdown-alert.markdown-alert-tip{border-left-color:var(--borderColor-success-emphasis)}.markdown-body .markdown-alert.markdown-alert-tip .markdown-alert-title{color:var(--fgColor-success)}.markdown-body .markdown-alert.markdown-alert-caution{border-left-color:var(--borderColor-danger-emphasis)}.markdown-body .markdown-alert.markdown-alert-caution .markdown-alert-title{color:var(--fgColor-danger)}.markdown-body>*:first-child>.heading-element:first-child{margin-top:0 !important}.markdown-body .highlight pre:has(+.zeroclipboard-container){min-height:52px}`,He=":host{display:block;width:100%}.message-round{margin-bottom:16px;width:100%}.user-message-container{display:flex;flex-direction:row-reverse;align-items:flex-start;margin-bottom:15px;position:relative}.assistant-message-container{display:flex;flex-direction:row;align-items:flex-start;margin-bottom:15px;position:relative}.message-bubble{max-width:85%;position:relative}.user-message{padding:15px 10px;background:#f5f7ff;font-size:14px;color:#1F2328;border-radius:6px;box-shadow:0 1px 2px 0 rgba(0, 0, 0, 0.1)}.assistant-message{padding:15px 10px;background-color:#fff;border-radius:6px;font-size:14px;color:#1F2328;box-shadow:0 1px 2px 0 rgba(0, 0, 0, 0.05)}.message-time{font-size:12px;color:#999;margin-top:4px}.user-message-container .message-time{color:rgba(255, 255, 255, 0.7)}.loading-dots{display:flex;align-items:center;padding:12px 16px;background-color:#f0f0f0;border-radius:18px;border-top-left-radius:4px}.loading-dots span{width:8px;height:8px;margin:0 3px;background-color:#999;border-radius:50%;display:inline-block;animation:dot-pulse 1.5s infinite ease-in-out}.loading-dots span:nth-child(2){animation-delay:0.2s}.loading-dots span:nth-child(3){animation-delay:0.4s}@keyframes dot-pulse{0%,80%,100%{transform:scale(0.8);opacity:0.5}40%{transform:scale(1.2);opacity:1}}.file-view{margin-top:8px;padding:12px;background-color:#f8f9fa;border:1px solid #e9ecef;border-radius:6px;font-size:14px;word-break:break-all;color:#2c3e50;line-height:1.5;box-shadow:0 1px 2px rgba(0, 0, 0, 0.05)}.input-view{margin-top:8px;margin-bottom:10px;padding-bottom:10px;border:1px solid #e9ecef;border-radius:6px;overflow:hidden;background-color:#ffffff;box-shadow:0 1px 2px rgba(0, 0, 0, 0.05)}.input-label-container{display:flex;align-items:center;margin-bottom:4px;background-color:#f8f9fa;border-bottom:1px solid #e9ecef;padding:8px 12px}.copy-input-button{background:transparent;border:none;cursor:pointer;padding:2px;display:flex;align-items:center;justify-content:center;color:#8c8c8c;opacity:0.7;transition:opacity 0.2s ease;margin-left:5px}.copy-input-button:hover{opacity:1;color:#1677ff}.input-label{font-size:13px;font-weight:600;color:#495057}.input-value{padding:12px 12px 0px;font-size:14px;line-height:1.5;color:#2c3e50;background-color:#ffffff;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis}.input-metadata{font-size:13px;margin-top:8px}.message-actions{display:flex;align-items:center;gap:8px;transition:opacity 0.2s ease;margin-top:15px}.action-button{display:inline-flex;align-items:center;justify-content:center;border:1px solid #d9d9d9;border-radius:6px;padding:4px 15px;font-size:14px;height:32px;background-color:white;cursor:pointer;color:rgba(0, 0, 0, 0.88);transition:all 0.2s}.action-button:hover{color:#1677ff;border-color:#1677ff}.action-button.primary{color:#1677ff;border-color:#1677ff}.action-button.primary:hover{color:#4096ff;border-color:#4096ff}.action-button.icon-only{padding:4px 8px;margin-right:9px}.button-icon{margin-right:8px;display:flex;align-items:center;justify-content:center}.icon-only .button-icon{margin-right:0}.file-list{display:flex;flex-wrap:wrap;gap:12px;margin-top:8px}.file-item{display:flex;flex-direction:column;align-items:center;width:80px;cursor:pointer;padding:8px;border-radius:4px;transition:background-color 0.3s;margin:5px 10px}.file-item:hover{background-color:rgba(0, 0, 0, 0.04)}.file-icon{margin-bottom:4px}.file-name{font-size:12px;text-align:center;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#8B4513}.action-button.active{color:#1677ff;background-color:rgba(22, 119, 255, 0.1)}.action-button.active .button-icon svg{stroke:#1677ff}.inputs-container{margin-top:8px;display:flex;flex-direction:column;gap:8px}.file-list{display:flex;flex-direction:column;gap:8px}.file-item{display:flex;align-items:center;padding:8px 12px;background-color:#f5f5f5;border-radius:6px;cursor:pointer;transition:background-color 0.2s}.file-item:hover{background-color:#e8e8e8}.file-icon{margin-right:8px}.file-name{font-size:14px;color:#333;word-break:break-word}.input-view{background-color:#f9f9f9;border-radius:8px;padding:12px;border:1px solid #e8e8e8}.input-label-container{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.input-label{font-weight:500;color:#333}.input-value{color:#666;line-height:1.5;word-break:break-word;max-height:200px;overflow-y:auto}.copy-input-button{background:none;border:none;cursor:pointer;color:#1677ff;padding:4px;border-radius:4px;display:flex;align-items:center;justify-content:center}.copy-input-button:hover{background-color:rgba(22, 119, 255, 0.1)}.input-metadata{font-size:14px;color:#666;padding:4px 0}.file-card{display:flex;align-items:center;padding:12px;background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 200px 100px no-repeat, #fff;border-radius:8px;cursor:pointer;transition:background-color 0.2s;border:1px solid #e8e8e8;margin-top:8px}.file-card:hover{background-color:#f0f0f0}.file-card-icon{margin-right:12px;flex-shrink:0;border-radius:12px;background:#0d75fb;width:48px;height:48px;display:flex;align-items:center;justify-content:center}.file-card-icon img{width:40px;height:40px}.file-card-content{display:flex;flex-direction:column;flex:1;min-width:0}.file-card-type{font-size:14px;color:#8c8c8c;margin-bottom:4px}.file-card-name{font-size:16px;font-weight:500;color:#333;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%}.copy-card-button{background:none;border:none;cursor:pointer;color:#8c8c8c;padding:8px;border-radius:4px;display:flex;align-items:center;justify-content:center;margin-left:8px;transition:color 0.2s}.copy-card-button:hover{color:#1677ff}.avatar{width:32px;height:32px;border-radius:50%;overflow:hidden;flex-shrink:0}.avatar img{width:100%;height:100%;object-fit:cover}.user-avatar{margin-left:8px}.assistant-avatar{margin-right:8px}.retry-button{background-color:#ff6b35 !important;color:white !important;border:none !important}.retry-button:hover{background-color:#e55a2b !important}.retry-button .button-icon{color:white !important}",qe=class{constructor(e){i(this,"message");i(this,"showFeedbackButtons",!0);i(this,"botId");i(this,"messageChange");i(this,"feedbackStatus",null);i(this,"userAvatar");i(this,"assistantAvatar");i(this,"showCopyButton",!0);i(this,"filePreviewMode","window");i(this,"filePreviewRequest");i(this,"retryRequest");S(this,e),this.messageChange=m(this,"messageChange"),this.filePreviewRequest=m(this,"filePreviewRequest"),this.retryRequest=m(this,"retryRequest"),F.use($e),F.setOptions({breaks:!0,gfm:!0})}get hostElement(){return _(this)}componentWillLoad(){this.message&&this.message.feedback&&(this.feedbackStatus=this.message.feedback.rating)}copyMessageContent(){this.message.answer&&navigator.clipboard.writeText(this.message.answer).then(()=>{alert("内容已复制到剪贴板")}).catch(e=>{y.emitError({error:e,message:"复制内容失败"}),console.error("复制失败:",e)})}renderUserMessage(){var e,t;return(t=(e=this.message)==null?void 0:e.query)!=null&&t.trim()?o("div",{class:{"user-message-container":!0}},this.userAvatar&&o("div",{class:"avatar user-avatar"},o("img",{src:this.userAvatar,alt:"用户头像"})),o("div",{class:"message-bubble user-message"},o("div",null,this.message.query),this.renderInputs())):null}renderAssistantMessage(){if(!this.message.answer&&!this.message.isStreaming)return null;const e=this.message.isStreaming&&!this.message.answer,t=this.message.answer?F(this.message.answer):"";return o("div",{class:{"assistant-message-container":!0}},this.assistantAvatar&&o("div",{class:"avatar assistant-avatar"},o("img",{src:this.assistantAvatar,alt:"助手头像"})),o("div",{class:"message-bubble "},o("div",{class:"assistant-message"},o("div",{class:"markdown-content markdown-body",innerHTML:e?"请稍等...":t})),!e&&this.message.answer&&!this.message.isStreaming&&o("div",{class:"message-actions"},this.message.showRetryButton&&o("button",{class:"action-button retry-button",onClick:()=>this.handleRetry(),title:"重试"},o("span",{class:"button-icon"},o("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},o("polyline",{points:"23 4 23 10 17 10"}),o("polyline",{points:"1 20 1 14 7 14"}),o("path",{d:"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"}))),"重试"),this.showCopyButton&&o("button",{class:"action-button",onClick:()=>this.copyMessageContent(),title:"复制内容"},o("span",{class:"button-icon"},o("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},o("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),o("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"}))),"复制"),this.showFeedbackButtons&&o(o.Fragment,null,o("button",{class:`action-button icon-only ${this.feedbackStatus==="like"?"active":""}`,title:"赞",onClick:()=>this.handleLike()},o("span",{class:"button-icon"},o("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},o("path",{d:"M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"})))),o("button",{class:`action-button icon-only ${this.feedbackStatus==="dislike"?"active":""}`,title:"踩",onClick:()=>this.handleDislike()},o("span",{class:"button-icon"},o("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},o("path",{d:"M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"}))))))))}getFileName(e){const t=e.split("/");return t[t.length-1]}async getCosPreviewUrl(e){var t;try{const n=await M({url:"/sdk/v1/files/presigned-url",method:"GET",params:{cos_key:e}});if(n.success&&((t=n.data)!=null&&t.file_url)){const r=n.data.file_url;return`${r}${r.includes("?")?"&":"?"}ci-process=doc-preview&copyable=1&dstType=html`}return null}catch(n){return x.captureError(n,{action:"getCosPreviewUrl",component:"pcm-chat-message",title:"获取预览URL失败"}),y.emitError({error:n,message:"获取预览URL失败"}),console.error("获取预览URL失败:",n),null}}handleContentClick(e,t,n="text"){this.filePreviewRequest.emit({fileName:e,content:t,contentType:n})}async handleFileClick(e,t){const n=await this.getCosPreviewUrl(e);n?this.filePreviewMode==="drawer"?this.filePreviewRequest.emit({url:n,fileName:t,contentType:"file"}):window.open(n,"_blank"):(console.error("无法获取预览URL"),x.captureError("无法获取预览URL",{action:"handleFileClick",component:"pcm-chat-message",title:"无法获取预览URL"}),y.emitError({error:"无法获取预览URL",message:"无法获取预览URL"}))}renderFileItem(e,t,n){return o("div",{key:n,class:"file-card",onClick:()=>this.handleFileClick(t,e)},o("div",{class:"file-card-icon",style:{background:"linear-gradient(45deg, #ff5600, #ff8344)"}},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"})),o("div",{class:"file-card-content"},o("div",{class:"file-card-type"},"[附件信息]"),o("div",{class:"file-card-name"},e)))}renderInputs(){return!this.message.inputs||!Object.keys(this.message.inputs).some(t=>{const n=this.message.inputs[t];return n!=null&&n!==""&&!t.startsWith("hide_")&&t!=="answer"})?null:o("div",{class:"inputs-container"},Object.keys(this.message.inputs).map((t,n)=>{const r=this.message.inputs[t];if(r&&!t.startsWith("hide_")&&t!=="answer"){if(t==="file_url"){const s=this.message.inputs.file_name||this.getFileName(r);return this.renderFileItem(s,r,n)}else if(t==="file_urls"){const s=typeof r=="string"?r.split(","):[r.toString()],a=this.message.inputs.file_names?typeof this.message.inputs.file_names=="string"?this.message.inputs.file_names.split(","):[this.message.inputs.file_names.toString()]:[];return o("div",{key:n},s.map((l,d)=>{const c=a[d]||this.getFileName(l);return this.renderFileItem(c,l,d)}))}else if(t==="job_info"||t==="rule"){const s=t==="job_info"?"职位信息":"评估规则",a=typeof r=="string"?r:JSON.stringify(r,null,2);return o("div",{key:n,class:"file-card",onClick:()=>this.handleContentClick(s,a)},o("div",{class:"file-card-icon",style:{background:"#0d75fb"}},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"})),o("div",{class:"file-card-content"},o("div",{class:"file-card-type"},"[",s,"]"),o("div",{class:"file-card-name"},a.length>50?a.substring(0,50)+"...":a)))}else if(t==="input")return o("div",{key:n,class:"input-metadata"},`${r}`)}return null}))}async handleLike(){const e=this.feedbackStatus==="like"?null:"like";await this.submitFeedback(e)}async handleDislike(){const e=this.feedbackStatus==="dislike"?null:"dislike";await this.submitFeedback(e)}async submitFeedback(e){if(!this.message.id){console.error("消息ID不存在，无法提交反馈"),x.captureError("消息ID不存在，无法提交反馈",{action:"submitFeedback",component:"pcm-chat-message",title:"无法提交反馈"}),y.emitError({error:"消息ID不存在，无法提交反馈",message:"消息ID不存在，无法提交反馈"});return}try{const t=await M({url:`/sdk/v1/chat/messages/${this.message.id}/feedbacks`,method:"POST",data:{rating:e,bot_id:this.botId,content:""}});t.success?(this.feedbackStatus=e,this.message&&this.message.feedback&&(this.message={...this.message,feedback:{...this.message.feedback,rating:e}})):console.error("反馈提交失败:",t.message)}catch(t){console.error("提交反馈时发生错误:",t),x.captureError(t,{action:"submitFeedback",component:"pcm-chat-message",title:"提交反馈失败"})}}handleRetry(){this.message.id&&this.retryRequest.emit(this.message.id)}render(){return o("div",{key:"31deecda66c53dced0654dd4201353e8419effa0",class:"message-round"},this.renderUserMessage(),this.renderAssistantMessage())}};qe.style=Pe+He;const Oe=":host{display:block}.drawer-container{position:fixed;top:0;left:0;width:0;height:0;overflow:visible}.drawer-mask{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.45);opacity:0;visibility:hidden;transition:opacity 0.3s ease, visibility 0.3s ease}.mask-visible{opacity:1;visibility:visible}.drawer-content{position:fixed;top:0;right:0;bottom:0;display:flex;flex-direction:column;background-color:#fff;box-shadow:-2px 0 8px rgba(0, 0, 0, 0.15);transform:translateX(100%);transition:transform 0.3s cubic-bezier(0.23, 1, 0.32, 1)}.drawer-content-visible{transform:translateX(0)}.drawer-header{display:flex;align-items:center;justify-content:space-between;padding:16px 24px;color:rgba(0, 0, 0, 0.85);border-bottom:1px solid #f0f0f0}.drawer-title{flex:1;margin:0;font-size:16px;line-height:22px;font-weight:500;color:rgba(0, 0, 0, 0.85);overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.drawer-close{padding:0;background:transparent;border:none;outline:none;cursor:pointer;font-size:16px;color:rgba(0, 0, 0, 0.45);transition:color 0.3s}.drawer-close:hover{color:rgba(0, 0, 0, 0.85)}.drawer-body{flex:1;padding:24px;overflow:auto}@media (max-width: 768px){.drawer-content{width:100% !important}.drawer-header{padding:12px 16px}.drawer-body{padding:16px}}",Ne=class{constructor(e){i(this,"isOpen",!1);i(this,"drawerTitle","");i(this,"width","378px");i(this,"height","378px");i(this,"closable",!0);i(this,"maskClosable",!0);i(this,"mask",!0);i(this,"closed");i(this,"afterOpen");i(this,"afterClose");i(this,"zIndex",1e3);i(this,"bodyOverflowBeforeOpen","");i(this,"transitionEndHandler");i(this,"handleMaskClick",()=>{this.maskClosable&&this.handleClose()});i(this,"handleClose",()=>{this.isOpen=!1,this.closed.emit()});S(this,e),this.closed=m(this,"closed"),this.afterOpen=m(this,"afterOpen"),this.afterClose=m(this,"afterClose")}get hostElement(){return _(this)}async open(){this.isOpen=!0}async close(){this.isOpen=!1}visibleChanged(e){if(e){this.bodyOverflowBeforeOpen=document.body.style.overflow,document.body.style.overflow="hidden";const t=this.hostElement.shadowRoot.querySelector(".drawer-content");t&&(this.transitionEndHandler=()=>{this.afterOpen.emit()},t.addEventListener("transitionend",this.transitionEndHandler,{once:!0}))}else{document.body.style.overflow=this.bodyOverflowBeforeOpen;const t=this.hostElement.shadowRoot.querySelector(".drawer-content");t&&(this.transitionEndHandler=()=>{this.afterClose.emit()},t.addEventListener("transitionend",this.transitionEndHandler,{once:!0}))}}componentWillLoad(){const e=z.getItem("modal-zIndex");e&&(this.zIndex=e)}disconnectedCallback(){var t;this.isOpen&&(document.body.style.overflow=this.bodyOverflowBeforeOpen);const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".drawer-content");e&&this.transitionEndHandler&&e.removeEventListener("transitionend",this.transitionEndHandler)}render(){const e={width:this.width,zIndex:`${this.zIndex+1}`},t={zIndex:`${this.zIndex}`};return o("div",{key:"c0c12a379d5ebfa0dd3cc3d1bbdc3ac758a5006f",class:{"drawer-container":!0,"drawer-open":this.isOpen}},this.mask&&o("div",{key:"01a6db65de08dd54fbc1a52efeda576e6f71f4ae",class:{"drawer-mask":!0,"mask-visible":this.isOpen},style:t,onClick:this.handleMaskClick}),o("div",{key:"121cb9463b12c9ff287e1890c03281dd69d074a9",class:{"drawer-content":!0,"drawer-content-visible":this.isOpen},style:e},o("div",{key:"839536eb87451599dafdda11185935f51b59ce03",class:"drawer-header"},this.drawerTitle&&o("div",{key:"f02c896b38f2e089687f0baa96fea06f4303ebf0",class:"drawer-title"},this.drawerTitle),this.closable&&o("button",{key:"0ed699d106894d01242f105583860376f1228f48",class:"drawer-close",onClick:this.handleClose},o("svg",{key:"987102bc7f8fb3d86322a8d9029951db6cccb266",viewBox:"64 64 896 896",focusable:"false","data-icon":"close",width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},o("path",{key:"b2e5ea93efbab2fe2a76ed59872144cdb4ee9f6a",d:"M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 00203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"})))),o("div",{key:"d882ce5d71c4d2adeecab918a70a56b657e86b63",class:"drawer-body"},o("slot",{key:"9a822a390e1f2f20d287d29dbbbb0501b0989207"}))))}static get watchers(){return{isOpen:["visibleChanged"]}}};Ne.style=Oe;const Qe=":host{display:block}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;overflow-y:auto;padding:20px;z-index:1000}.fullscreen-overlay{padding:0}.modal-container{background:white;border-radius:8px;width:100%;max-width:800px;display:flex;flex-direction:column;position:relative;margin:auto}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.modal-container.fullscreen>div:not(.modal-header):not(.initial-upload){display:flex;flex-direction:column;flex:1;overflow:hidden;height:100%}.pc-layout{width:80%;max-width:800px;min-width:320px;min-height:400px}@media screen and (max-width: 768px){.pc-layout{width:95%;}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}}.video-preview.placeholder{display:flex;justify-content:center;align-items:center;background:#EAEAEA}.placeholder-status{color:#00000066}.placeholder-status p{font-size:16px}.waiting-message p{margin:0;font-size:16px;color:white;font-weight:500}.recording-container{width:100%;display:flex;flex-direction:column;align-items:center}.video-area{width:100%;display:flex;flex-direction:column;align-items:center}.stop-recording-button{width:100%;height:100%;font-size:16px;background:#f44336;border-radius:6px;color:white;border:none;cursor:pointer}.stop-recording-button:hover{background:#d32f2f}.play-audio-container{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0;}.header-left{display:flex;align-items:center;gap:8px}.header-left div{font-size:16px}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.chat-history{position:relative;flex:1;overflow-y:auto;padding:20px;scroll-behavior:smooth;min-height:200px;background:url(https://pcm-resource-1312611446.cos.ap-guangzhou.myqcloud.com/web/sdk/chat_bg.png);background-size:100%}.fullscreen .chat-history{height:auto;flex:1 1 auto}.message-input{padding:16px;border-top:1px solid #eee;display:flex;gap:8px;align-items:center}.message-input input{flex:1;padding:8px 12px;border:1px solid #ddd;border-radius:4px;outline:none;transition:border-color 0.2s ease}.message-input input:focus{border-color:#bbb}.message{margin-bottom:16px;opacity:1;transition:opacity 0.3s ease}.message-content{max-width:70%;padding:8px 12px;border-radius:8px;word-break:break-word}.message-content p{margin:0;word-break:break-word}.user-message{display:flex;justify-content:flex-end}.agent-message{display:flex;justify-content:flex-start}.user-message .message-content{background-color:#007bff;color:white}.agent-message .message-content{background-color:#f1f1f1}.message-time{font-size:12px;color:#999;margin-top:4px;display:block}.send-button{background-color:#1890ff;color:white;border:none;border-radius:4px;padding:8px 16px;cursor:pointer;font-weight:500}.send-button:disabled{background-color:#ccc;cursor:not-allowed}.empty-state{display:flex;justify-content:center;align-items:center;height:100%;color:#999;text-align:center}.loading-container{position:absolute;top:0;left:0;right:0;bottom:0;display:flex;flex-direction:column;justify-content:center;align-items:center;background-color:rgba(255, 255, 255, 0.98);z-index:1;opacity:1;transition:opacity 0.3s ease}.loading-container p{margin-top:16px;color:#666;font-size:14px}.loading-spinner{width:40px;height:40px;border:3px solid #f3f3f3;border-top:3px solid #1890ff;border-radius:50%;animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.messages-wrapper{width:100%;min-height:100%;display:flex;flex-direction:column;justify-content:flex-end}.messages-wrapper.has-overflow{justify-content:flex-start}.suggested-questions{display:flex;flex-direction:column;gap:8px;padding:16px}.suggested-question{display:flex;align-items:center;justify-content:space-between;padding:8px 12px;background-color:#f3f4f6;border-radius:4px;cursor:pointer;font-size:14px;color:#374151;transition:background-color 0.2s}.suggested-question:hover{background-color:#e5e7eb}.arrow-right{margin-left:8px}.loading-suggestions{display:flex;justify-content:center;padding:16px}.loading-spinner-small{width:20px;height:20px;border:2px solid #e5e7eb;border-top-color:#6b7280;border-radius:50%;animation:spin 1s linear infinite}.upload-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;color:#666;border-radius:4px;transition:background-color 0.2s}.upload-button:hover{background-color:rgba(0, 0, 0, 0.04)}.upload-button svg{width:20px;height:20px}.file-input{display:none}.selected-file{font-size:12px;color:#666;margin-left:8px;max-width:150px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.input-wrapper{flex:1;display:flex;align-items:center;border:1px solid #ddd;border-radius:4px;padding:0 4px;background:white}.input-wrapper input{border:none;flex:1;padding:8px;outline:none}.input-wrapper:focus-within{border-color:#bbb}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#999;cursor:pointer;padding:4px 8px;font-size:16px;line-height:1;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f0f0f0;color:#666}.initial-upload{padding:16px;display:flex;flex-direction:column;align-items:center;height:100%}.upload-section{max-width:600px;width:100%;text-align:center}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px}.submit-button:disabled{background:#ccc;cursor:not-allowed}.submit-button:hover:not(:disabled){background:#40a9ff}.category-select,.dimension-select{margin:30px 0}.category-options,.dimension-options{display:flex;flex-wrap:wrap;gap:10px;margin-top:10px}.category-button,.dimension-button{padding:12px 16px;border:1px solid #E5E5E5;border-radius:6px;background:white;cursor:pointer;transition:all 0.3s}.category-button:hover,.dimension-button:hover{background:#f5f5f5}.category-button.selected{background-image:linear-gradient(111deg, #4A9FFF 0%, #1058FF 100%);color:white}.dimension-button.selected{background-image:linear-gradient(111deg, #4A9FFF 0%, #1058FF 100%);color:white}.recording-section{border-top:1px solid #eee;display:flex;flex-direction:column;align-items:center;padding:20px;border-radius:14px 14px 0 0;flex:0 0 auto}.recording-section .video-preview{width:100%;height:200px;max-width:400px;position:relative;margin-bottom:10px;border:1px solid #ddd;border-radius:12px;overflow:hidden}.recording-section video{width:100%;height:100%;object-fit:cover}.recording-status{position:absolute;top:10px;left:10px;background-color:rgba(0, 0, 0, 0.6);color:white;padding:4px 8px;border-radius:4px;display:flex;align-items:center;gap:5px;font-size:14px;z-index:2}.recording-status .recording-dot{display:inline-block;width:10px;height:10px;background-color:red;border-radius:50%;margin-right:5px;animation:blink 1s infinite}.recording-status.warning{color:#ff4d4f;animation:blink 1s infinite}@keyframes blink{0%{opacity:1}50%{opacity:0.5}100%{opacity:1}}.recording-section .stop-recording-button{background-color:#f44336;color:white;border:none;cursor:pointer;font-weight:bold}.recording-section .stop-recording-button:hover{background-color:#d32f2f}.fullscreen{width:100vw;border-radius:0;height:100vh;display:flex;flex-direction:column;overflow-y:auto}.recording-controls{margin-top:10px;height:53px;width:100%;max-width:400px;display:flex;justify-content:center}.recording-controls .waiting-message{text-align:center;color:white;font-size:16px;background-image:linear-gradient(100deg, #4A9FFF 0%, #1058FF 100%);border-radius:6px;box-shadow:0 2px 8px rgba(0, 0, 0, 0.15);width:95%;display:flex;justify-content:center;align-items:center;cursor:pointer}.recording-controls .waiting-message.loading{background:#faad14}.recording-controls .waiting-message p{margin:0;font-size:16px;color:white;font-weight:500}.recording-controls .stop-recording-button{background-color:#dc3545;color:white;border:none;cursor:pointer;font-size:16px}.recording-controls .stop-recording-button:hover{background-color:#c82333}.recording-controls .stop-recording-button.disabled{background:#ccc;cursor:not-allowed}.recording-controls .stop-recording-button.disabled:hover{background:#ccc}.progress-container{display:flex;justify-content:space-between;align-items:center;width:100%;max-width:400px;margin-top:10px;padding:0 5px}.progress-bar-container{height:4px;background-color:#E5E5E5;border-radius:2px;overflow:hidden;margin-right:10px;width:75px}.progress-bar{height:100%;background-image:linear-gradient(111deg, #4A9FFF 0%, #1058FF 100%);border-radius:2px;transition:width 0.3s ease}.progress-text{font-size:14px;color:#666;white-space:nowrap}",Ve=class{constructor(e){i(this,"modalTitle","金牌HR大赛");i(this,"token");i(this,"isOpen",!1);i(this,"messages",[]);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"currentAssistantMessage","");i(this,"isLoading",!1);i(this,"currentStreamingMessage",null);i(this,"shouldAutoScroll",!0);i(this,"isLoadingHistory",!1);i(this,"streamComplete");i(this,"conversationStart");i(this,"someErrorEvent");i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",[]);i(this,"aiException",!1);i(this,"defaultQuery","请开始出题");i(this,"showInitialUpload",!0);i(this,"selectedJobCategory","");i(this,"jobCategories",["人力资源学生(实习)","人力资源专员","人力资源主管","人力资源经理","人力资源总监"]);i(this,"dimensions",["人力资源规划","招聘与配置","员工关系","培训与开发","薪酬与绩效","组织与人才发展"]);i(this,"selectedDimensions",[]);i(this,"isRecording",!1);i(this,"recordingStream",null);i(this,"recordedBlob",null);i(this,"mediaRecorder",null);i(this,"recordingTimeLeft",0);i(this,"showRecordingUI",!1);i(this,"recordingTimer",null);i(this,"recordingStartTime",0);i(this,"waitingToRecord",!1);i(this,"waitingTimer",null);i(this,"waitingTimeLeft",15);i(this,"videoRef",null);i(this,"totalQuestions",2);i(this,"currentQuestionNumber",0);i(this,"interviewComplete");i(this,"SCROLL_THRESHOLD",20);i(this,"maxRecordingTime",120);i(this,"countdownWarningTime",30);i(this,"showCountdownWarning",!1);i(this,"toEmail","");i(this,"callbackUrl","");i(this,"fullscreen",!1);i(this,"isUploadingVideo",!1);i(this,"requireResume",!1);i(this,"isPlayingAudio",!1);i(this,"audioUrl",null);i(this,"audioElement",null);i(this,"recordingError");i(this,"recordingStatusChange");i(this,"tokenInvalid");i(this,"enableVoice",!0);i(this,"enableAudio",!0);i(this,"displayContentStatus",!0);i(this,"isTaskCompleted",!1);i(this,"tokenInvalidListener");i(this,"isUserScrolling",!1);i(this,"isPageFocused",!0);i(this,"isWindowVisible",!0);i(this,"visibilityChangeListener");i(this,"blurListener");i(this,"focusListener");i(this,"handleClose",()=>{this.stopRecording(),this.modalClosed.emit()});i(this,"handleFileChange",async e=>{const t=e.target;t.files&&t.files.length>0&&(this.selectedFile=t.files[0])});i(this,"handleUploadClick",()=>{var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e==null||e.click()});i(this,"clearSelectedFile",()=>{var t;this.selectedFile=null,this.uploadedFileInfo=[];const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e&&(e.value="")});i(this,"handleScroll",()=>{var e;if(this.isUserScrolling){const t=(e=this.hostElement.shadowRoot)==null?void 0:e.querySelector(".chat-history");if(!t)return;const{scrollTop:n,scrollHeight:r,clientHeight:s}=t,a=r-n-s;this.shouldAutoScroll=a<=this.SCROLL_THRESHOLD}});i(this,"handleTouchStart",()=>{this.isUserScrolling=!0});i(this,"handleTouchEnd",()=>{setTimeout(()=>{this.isUserScrolling=!1},100)});i(this,"_wheelTimer",null);i(this,"handleWheel",()=>{this.isUserScrolling=!0,this._wheelTimer&&clearTimeout(this._wheelTimer),this._wheelTimer=setTimeout(()=>{this.isUserScrolling=!1},150)});i(this,"handleJobCategorySelect",e=>{this.selectedJobCategory=e});i(this,"handleDimensionSelect",e=>{this.selectedDimensions.includes(e)?this.selectedDimensions=this.selectedDimensions.filter(t=>t!==e):this.selectedDimensions=[...this.selectedDimensions,e]});i(this,"handleInitialSubmit",async()=>{if(this.requireResume&&!this.selectedFile){alert("请上传简历");return}if(!this.selectedJobCategory){alert("请选择职能类别");return}if(this.selectedDimensions.length===0){alert("请至少选择一个关注模块");return}if(this.requireResume&&(await this.uploadFile(),this.uploadedFileInfo.length===0))return;this.showInitialUpload=!1;const e=`我是一名${this.selectedJobCategory}，请您开始提问`;this.sendMessageToAPI(e)});i(this,"handlePlayAudio",async()=>{this.audioUrl&&(await this.playAudio(this.audioUrl),this.startWaitingToRecord())});i(this,"handleRetryRequest",e=>{const t=this.messages.find(n=>n.id===e);if(t){this.aiException=!1;const n=this.messages.map(r=>r.id===e?{...r,showRetryButton:!1}:r);this.messages=n,this.sendMessageToAPI(t.query)}});S(this,e),this.modalClosed=m(this,"modalClosed"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.someErrorEvent=m(this,"someErrorEvent"),this.interviewComplete=m(this,"interviewComplete"),this.recordingError=m(this,"recordingError"),this.recordingStatusChange=m(this,"recordingStatusChange"),this.tokenInvalid=m(this,"tokenInvalid")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}componentWillLoad(){this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},document.addEventListener("pcm-token-invalid",this.tokenInvalidListener),this.visibilityChangeListener=()=>{const e=!document.hidden;this.isWindowVisible=e,e||x.captureMessage("用户失去焦点 - 页面隐藏",{action:"visibilityChange",component:"pcm-hr-chat-modal",type:"visibility_hidden",timestamp:Date.now(),isRecording:this.isRecording,currentQuestionNumber:this.currentQuestionNumber,conversationId:this.conversationId})},this.blurListener=()=>{this.isPageFocused=!1,x.captureMessage("用户失去焦点 - 窗口失焦",{action:"windowBlur",component:"pcm-hr-chat-modal",type:"window_blur",timestamp:Date.now(),isRecording:this.isRecording,currentQuestionNumber:this.currentQuestionNumber,conversationId:this.conversationId})},this.focusListener=()=>{this.isPageFocused=!0},document.addEventListener("visibilitychange",this.visibilityChangeListener),window.addEventListener("blur",this.blurListener),window.addEventListener("focus",this.focusListener)}async uploadFile(){if(!(!this.selectedFile||this.uploadedFileInfo.length>0)){this.isUploading=!0;try{const e=await A(this.selectedFile,{},{tags:["resume"]});e&&(this.uploadedFileInfo=[{cos_key:e.cos_key,file_name:e.file_name,file_size:e.file_size,ext:e.ext}])}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),this.someErrorEvent.emit({error:e,message:"文件上传失败，请重试"}),x.captureError(e,{action:"uploadFile",component:"pcm-hr-chat-modal",title:"文件上传失败"})}finally{this.isUploading=!1}}}async sendMessageToAPI(e){this.isLoading=!0;let t="",n="";const r=new Date,s=`${r.getHours()}:${r.getMinutes().toString().padStart(2,"0")}`,a=e.trim()||(this.uploadedFileInfo.length>0?"请分析这个文件":""),l=this.messages.length>0?this.messages[this.messages.length-1]:null;l&&this.conversationId&&e!=="下一题"&&this.saveAnswer(this.conversationId,l.answer,a);const d=this.currentQuestionNumber>=this.totalQuestions&&e==="下一题",c={id:`temp-${Date.now()}`,time:s,query:a,answer:"",isStreaming:!0,conversation_id:this.conversationId,inputs:{},status:"normal",error:null};if(this.currentStreamingMessage=c,setTimeout(()=>{this.shouldAutoScroll=!0,this.scrollToBottom()},200),d){this.messages=[...this.messages,c],this.currentStreamingMessage=null,this.isLoading=!1,this.currentQuestionNumber++,this.isTaskCompleted=!0,await this.completeInterview(),this.interviewComplete.emit({conversation_id:this.conversationId,total_questions:this.totalQuestions});return}const p={response_mode:"streaming",conversation_id:this.conversationId,query:a,bot_id:"3022316191018880"};if(p.inputs={job_info:this.selectedJobCategory,dimensional_info:this.selectedDimensions.join(","),email:this.toEmail,callback_url:this.callbackUrl,display_content_status:this.displayContentStatus?"1":"0"},this.uploadedFileInfo.length>0){const u=this.uploadedFileInfo.map(g=>g.cos_key).join(",");p.inputs.file_urls=u}await te({url:"/sdk/v1/chat/chat-messages",method:"POST",data:p,onMessage:u=>{if(u.conversation_id&&!this.conversationId&&(this.conversationId=u.conversation_id,this.conversationStart.emit({conversation_id:u.conversation_id,event:u.event,message_id:u.message_id,id:u.id})),u.event==="node_finished"&&u.data.inputs&&u.data.inputs.LLMText&&(n=u.data.inputs.LLMText),u.event==="node_finished"&&u.data.title&&u.data.title.includes("异常请重试")&&(this.currentStreamingMessage.showRetryButton=!0,this.aiException=!0),u.event==="node_finished"&&u.data.title&&u.data.title.includes("题号赋值")&&(this.currentQuestionNumber=u.data.process_data.questions_number-1),u.event==="message"&&(u.event==="agent_message"||u.event==="message")&&u.answer){t+=u.answer;const g={...this.currentStreamingMessage,answer:t,isStreaming:!0};this.currentStreamingMessage=g,setTimeout(()=>{this.scrollToBottom()},200)}u.event==="message_end"&&this.streamComplete.emit({conversation_id:u.conversation_id||"",event:u.event,message_id:u.message_id,id:u.id})},onError:u=>{console.error("发生错误:",u),this.someErrorEvent.emit({error:u,message:"消息发送失败，请稍后再试"}),x.captureError(u,{action:"sendMessageToAPI",component:"pcm-hr-chat-modal",message:e,title:"消息发送失败"}),this.messages=[...this.messages,{...c,answer:"抱歉，发生了错误，请稍后再试。",error:u,isStreaming:!1}],this.currentStreamingMessage=null,this.isLoading=!1},onComplete:async()=>{this.isLoading=!1;const u=this.currentStreamingMessage;if(u.isStreaming=!1,this.currentStreamingMessage=null,this.messages=[...this.messages,u],!this.aiException)if(this.enableAudio&&u&&u.answer){const g=n||u.answer;if(g){const h=await Y(g);this.enableVoice?(await this.playAudio(h),this.startWaitingToRecord()):this.audioUrl=h}}else this.enableAudio||this.startWaitingToRecord()}})}async saveAnswer(e,t,n){try{await M({url:"/sdk/v1/hr_competition/answer",method:"POST",data:{conversation_id:e,question:t,answer:n}})}catch(r){console.error("保存答案失败:",r),this.someErrorEvent.emit({error:r,message:"保存答案失败，请稍后再试"}),x.captureError(r,{action:"saveAnswer",component:"pcm-hr-chat-modal",conversationId:e,title:"保存答案失败"})}}scrollToBottom(){var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".chat-history");e&&this.isOpen&&(e.scrollTop=e.scrollHeight)}async loadHistoryMessages(){if(!this.conversationId)return;this.isLoadingHistory=!0;let e=!1;try{const t=await M({url:`/sdk/v1/hr_competition/${this.conversationId}`,method:"GET"});t.success&&t.data&&t.data.ended==1&&(e=!0);const n=await M({url:"/sdk/v1/chat/messages",method:"GET",data:{conversation_id:this.conversationId,bot_id:"3022316191018880",limit:20}});if(n.success&&n.data){const s=(n.data.data||[]).map(l=>{const d=new Date(l.created_at*1e3),c=d.getHours().toString().padStart(2,"0"),p=d.getMinutes().toString().padStart(2,"0"),u=`${c}:${p}`,{inputs:g,...h}=l;return{...h,time:u,isStreaming:!1,status:l.status==="error"?"error":"normal"}});this.messages=s;const a=s.filter(l=>!(l.answer&&(l.answer.includes("请求超时")||l.answer.includes("网络有点拥堵")||l.status==="error")));this.currentQuestionNumber=a.length>0?a.length:0}}catch(t){console.error("加载历史消息或面试状态失败:",t),this.someErrorEvent.emit({error:t,message:"加载历史消息失败，请刷新重试"}),x.captureError(t,{action:"loadHistoryMessages",component:"pcm-hr-chat-modal",conversationId:this.conversationId,title:"加载历史消息失败"})}finally{this.isLoadingHistory=!1,setTimeout(async()=>{if(this.shouldAutoScroll=!0,this.scrollToBottom(),this.conversationId&&this.messages.length>0&&!e){const t=this.messages[this.messages.length-1];if(t&&t.answer&&this.enableAudio){const n=await Y(t.answer);this.enableVoice?(await this.playAudio(n),this.startWaitingToRecord()):this.audioUrl=n}else this.enableAudio?this.sendMessageToAPI("下一题"):this.startWaitingToRecord()}else e&&(this.currentQuestionNumber++,this.isTaskCompleted=!0,setTimeout(()=>{this.interviewComplete.emit()},100))},300)}}async handleIsOpenChange(e){e&&(await j(this.token),this.conversationId&&(this.showInitialUpload=!1,await this.loadHistoryMessages()))}startWaitingToRecord(){this.waitingTimer&&clearInterval(this.waitingTimer),this.recordingTimer&&clearInterval(this.recordingTimer),this.waitingToRecord=!0,this.waitingTimeLeft=15,this.waitingTimer=setInterval(()=>{this.waitingTimeLeft--,this.waitingTimeLeft<=0&&(clearInterval(this.waitingTimer),this.waitingTimer=null,this.waitingToRecord=!1,this.startRecording())},1e3)}async startRecording(){try{const e=await navigator.mediaDevices.getUserMedia({audio:!0,video:{width:{ideal:1280},height:{ideal:720},frameRate:{ideal:30}}});this.recordingStream=e,this.showRecordingUI=!0,this.showCountdownWarning=!1,this.videoRef=null,this.setupVideoPreview(e);const t=this.getSupportedMimeType();let n;try{n=new MediaRecorder(e,{mimeType:t,videoBitsPerSecond:8e5,audioBitsPerSecond:64e3})}catch(s){console.warn("指定的MIME类型不受支持，使用默认设置:",s);try{n=new MediaRecorder(e,{videoBitsPerSecond:8e5,audioBitsPerSecond:64e3})}catch{try{n=new MediaRecorder(e)}catch(l){this.recordingError.emit({type:"recorder_creation_failed",message:"无法创建媒体录制器，您的浏览器可能不支持此功能",details:l}),this.showRecordingUI=!1;return}}}this.mediaRecorder=n;const r=[];n.ondataavailable=s=>{s.data.size>0&&r.push(s.data)},n.onerror=s=>{this.recordingError.emit({type:"recording_error",message:"录制过程中发生错误",details:s}),this.stopRecording()},n.onstop=()=>{try{const s=t||"video/mp4",a=new Blob(r,{type:s});if(a.size===0){this.recordingError.emit({type:"empty_recording",message:"录制的视频为空"}),this.showRecordingUI=!1;return}this.recordedBlob=a,this.recordingStatusChange.emit({status:"stopped",details:{duration:Math.floor((Date.now()-this.recordingStartTime)/1e3),size:a.size,type:a.type}}),this.uploadRecordedVideo()}catch(s){this.recordingError.emit({type:"processing_error",message:"处理录制视频时出错",details:s}),this.showRecordingUI=!1,x.captureError(s,{action:"uploadRecordedVideo",component:"pcm-hr-chat-modal",title:"处理录制视频时出错"})}};try{n.start(),this.isRecording=!0,this.recordingStartTime=Date.now(),this.recordingTimeLeft=this.maxRecordingTime,this.recordingStatusChange.emit({status:"started",details:{maxDuration:this.maxRecordingTime,mimeType:n.mimeType}})}catch(s){this.recordingError.emit({type:"start_failed",message:"开始录制失败，请检查您的设备权限",details:s}),this.showRecordingUI=!1,x.captureError(s,{action:"startRecording",component:"pcm-hr-chat-modal",title:"开始录制失败"});return}this.recordingTimer=setInterval(()=>{const s=Math.floor((Date.now()-this.recordingStartTime)/1e3);this.recordingTimeLeft=Math.max(0,this.maxRecordingTime-s),this.recordingTimeLeft<=this.countdownWarningTime&&!this.showCountdownWarning&&(this.showCountdownWarning=!0),this.recordingTimeLeft<=0&&this.stopRecording()},1e3)}catch(e){console.error("无法访问摄像头或麦克风:",e),this.showRecordingUI=!1,this.recordingError.emit({type:"media_access_failed",message:"无法访问摄像头或麦克风，请确保已授予权限",details:e}),x.captureError(e,{action:"startRecording",component:"pcm-hr-chat-modal",title:"无法访问摄像头或麦克风"})}}setupVideoPreview(e){setTimeout(()=>{var n;const t=(n=this.hostElement.shadowRoot)==null?void 0:n.querySelector("video");if(t&&e)try{t.srcObject=e,t.play().catch(r=>{console.error("视频播放失败:",r),this.someErrorEvent.emit({error:r,message:"视频播放失败"}),x.captureError(r,{action:"setupVideoPreview",component:"pcm-hr-chat-modal",title:"录制视频显示失败"})})}catch(r){console.warn("设置srcObject失败，尝试替代方法:",r);try{const s=URL.createObjectURL(e);t.src=s,t.onended=()=>{URL.revokeObjectURL(s)}}catch(s){console.error("创建对象URL失败:",s),this.someErrorEvent.emit({error:s,message:"创建对象URL失败"}),x.captureError(s,{action:"setupVideoPreview",component:"pcm-hr-chat-modal",title:"创建对象URL失败"})}}else console.warn("未找到视频元素或媒体流无效")},100)}getSupportedMimeType(){const e=["video/webm;codecs=vp8,opus","video/webm;codecs=vp9,opus","video/webm","video/mp4","video/mp4;codecs=h264,aac",""];if(!window.MediaRecorder)return console.warn("MediaRecorder API不可用"),"";for(const t of e){if(!t)return"";try{if(MediaRecorder.isTypeSupported(t))return t}catch(n){console.warn(`检查MIME类型支持时出错 ${t}:`,n)}}return console.warn("没有找到支持的MIME类型，将使用浏览器默认值"),""}stopRecording(){this.mediaRecorder&&this.isRecording&&(this.mediaRecorder.stop(),this.isRecording=!1,this.recordingTimer&&(clearInterval(this.recordingTimer),this.recordingTimer=null),this.recordingStream&&(this.recordingStream.getTracks().forEach(e=>e.stop()),this.recordingStream=null),this.videoRef=null)}async uploadRecordedVideo(){if(this.recordedBlob)try{this.isUploadingVideo=!0,this.showRecordingUI=!1;const t=`answer.${this.recordedBlob.type.includes("webm")?"webm":"mp4"}`,n=new File([this.recordedBlob],t,{type:this.recordedBlob.type}),r=await A(n,{},{tags:["other"]});if(r)await this.saveVideoAnswer(r.cos_key),this.sendMessageToAPI("下一题");else throw new Error("视频上传失败")}catch(e){console.error("视频上传错误:",e),this.recordingError.emit({type:"upload_failed",message:"视频上传失败",details:e}),x.captureError(e,{action:"uploadRecordedVideo",component:"pcm-hr-chat-modal",title:"视频上传失败"})}finally{this.isUploadingVideo=!1,this.showRecordingUI=!1,this.recordedBlob=null}}async saveVideoAnswer(e){if(this.conversationId)try{const t=this.messages.length>0?this.messages[this.messages.length-1]:null;if(!t)return;await M({url:"/sdk/v1/hr_competition/answer",method:"POST",data:{conversation_id:this.conversationId,question:t.answer,file_url:e}})}catch(t){console.error("保存视频答案失败:",t),this.someErrorEvent.emit({error:t,message:"保存视频答案失败"}),x.captureError(t,{action:"saveVideoAnswer",component:"pcm-hr-chat-modal",conversationId:this.conversationId,title:"保存视频答案失败"})}}async completeInterview(){if(this.conversationId)try{await M({url:`/sdk/v1/hr_competition/${this.conversationId}/end`,method:"POST"})}catch(e){console.error("发送面试完成请求失败:",e),this.someErrorEvent.emit({error:e,message:"发送面试完成请求失败"}),x.captureError(e,{action:"completeInterview",component:"pcm-hr-chat-modal",conversationId:this.conversationId,title:"发送面试完成请求失败"})}}playAudio(e){return new Promise(t=>{this.isPlayingAudio=!0,this.audioUrl=e,this.audioElement||(this.audioElement=new Audio),this.audioElement.src=e,this.audioElement.onended=()=>{this.isPlayingAudio=!1,this.audioUrl=null,t()},this.audioElement.onerror=()=>{console.error("音频播放错误"),this.isPlayingAudio=!1,this.audioUrl=null,this.someErrorEvent.emit({error:"音频播放错误",message:"音频播放错误"}),x.captureMessage("音频播放错误",{action:"playAudio",component:"pcm-hr-chat-modal",title:"音频播放错误"}),t()},this.audioElement.play().catch(n=>{console.error("音频播放失败:",n),this.isPlayingAudio=!1,this.audioUrl=null,this.someErrorEvent.emit({error:n,message:"音频播放失败"}),x.captureError(n,{title:"音频播放失败",action:"playAudio",component:"pcm-hr-chat-modal"}),t()})})}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.audioElement&&(this.audioElement.pause(),this.audioElement.src="",this.audioElement=null),this.audioUrl&&(URL.revokeObjectURL(this.audioUrl),this.audioUrl=null),this.waitingTimer&&(clearInterval(this.waitingTimer),this.waitingTimer=null),this.recordingTimer&&(clearInterval(this.recordingTimer),this.recordingTimer=null),this.stopRecording(),this.visibilityChangeListener&&document.removeEventListener("visibilitychange",this.visibilityChangeListener),this.blurListener&&window.removeEventListener("blur",this.blurListener),this.focusListener&&window.removeEventListener("focus",this.focusListener)}render(){if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=()=>o("div",{class:"video-preview"},o("video",{autoPlay:!0,playsInline:!0,muted:!0,style:{transform:"scaleX(-1)"},ref:a=>{a&&this.recordingStream&&!this.videoRef&&(this.videoRef=a)}}),o("div",{class:{"recording-status":!0,warning:this.showCountdownWarning}},o("span",{class:"recording-dot"}),o("span",null,"录制中 ",Math.floor(this.recordingTimeLeft/60),":",(this.recordingTimeLeft%60).toString().padStart(2,"0"),this.showCountdownWarning&&" (即将自动完成)"))),s=()=>this.isTaskCompleted?o("div",{class:"placeholder-status"},o("p",null,"面试已完成，感谢您的参与！")):this.isPlayingAudio?o("div",{class:"placeholder-status"},o("p",null,"正在播放问题，请听完后准备回答...")):this.isUploadingVideo?o("div",{class:"placeholder-status"},o("p",null,"正在上传视频，请稍候...")):this.isLoading||this.currentStreamingMessage?o("div",{class:"placeholder-status"},o("p",null,"请等待题目...")):this.waitingToRecord?o("div",{class:"placeholder-status"},o("p",null,"请准备好，",this.waitingTimeLeft,"秒后将开始录制您的回答...")):o("div",{class:"placeholder-status default-status"},o("p",null,"准备中..."));return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),this.showInitialUpload?o("div",{class:"initial-upload"},o("div",{class:"upload-section"},this.requireResume&&o(o.Fragment,null,o("h3",null,"开始前，请上传您的简历"),o("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?o("div",{class:"file-item"},o("div",{class:"file-item-content"},o("span",{class:"file-icon"},"📝"),o("span",{class:"file-name"},this.selectedFile.name)),o("button",{class:"remove-file",onClick:a=>{a.stopPropagation(),this.clearSelectedFile()}},"×")):o("div",{class:"upload-placeholder"},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),o("p",{class:"upload-text"},"点击上传简历"),o("p",{class:"upload-hint"},"支持 txt、 markdown、 pdf、 docx、  md 格式")))),o("div",{class:"category-select"},o("h3",null,"请选择您的职能类别（单选）"),o("div",{class:"category-options"},this.jobCategories.map(a=>o("button",{class:{"category-button":!0,selected:this.selectedJobCategory===a},onClick:()=>this.handleJobCategorySelect(a)},a)))),o("div",{class:"dimension-select"},o("h3",null,"请选择关注的模块（可多选）"),o("div",{class:"dimension-options"},this.dimensions.map(a=>o("button",{class:{"dimension-button":!0,selected:this.selectedDimensions.includes(a)},onClick:()=>this.handleDimensionSelect(a)},a)))),o("button",{class:"submit-button",disabled:this.requireResume&&!this.selectedFile||!this.selectedJobCategory||this.selectedDimensions.length===0||this.requireResume&&this.isUploading,onClick:this.handleInitialSubmit},this.requireResume&&this.isUploading?"上传中...":"开始面试")),this.requireResume&&o("input",{type:"file",class:"file-input",onChange:this.handleFileChange,accept:".pdf,.doc,.docx,.txt"})):o("div",{style:{height:"100%"}},o("div",{class:"chat-history",onScroll:this.handleScroll,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onWheel:this.handleWheel},this.isLoadingHistory?o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",null,"加载历史消息中...")):o("div",null,this.messages.map(a=>o("div",{id:`message_${a.id}`,key:a.id},o("pcm-chat-message",{message:a,onRetryRequest:l=>this.handleRetryRequest(l.detail),showCopyButton:!1,showFeedbackButtons:!1,onMessageChange:l=>{const d=this.messages.map(c=>c.id===a.id?{...c,...l.detail}:c);this.messages=d}}))),this.currentStreamingMessage&&o("div",{id:`message_${this.currentStreamingMessage.id}`},o("pcm-chat-message",{message:this.currentStreamingMessage})),this.messages.length===0&&!this.currentStreamingMessage&&o("div",{class:"empty-state"},o("p",null,"请上传简历开始面试")))),o("div",{class:"recording-section"},o("div",{class:"recording-container"},o("div",{class:"video-area"},this.showRecordingUI?r():o("div",{class:"video-preview placeholder"},s())),o("div",{class:"progress-container"},o("div",{class:"progress-bar-container"},o("div",{class:"progress-bar",style:{width:`${Math.max(0,this.currentQuestionNumber-1)/this.totalQuestions*100}%`}})),o("div",{class:"progress-text"},"已完成",Math.max(0,this.currentQuestionNumber-1),"/",this.totalQuestions)),o("div",{class:"recording-controls"},this.showRecordingUI?o("button",{class:"stop-recording-button",onClick:()=>this.stopRecording()},"完成本题回答"):o("div",{class:"waiting-message"},(()=>!this.enableVoice&&this.audioUrl&&!this.isPlayingAudio?o("div",{class:"play-audio-container",onClick:this.handlePlayAudio},o("p",null,o("svg",{viewBox:"0 0 24 24",width:"24",height:"24",fill:"currentColor",style:{verticalAlign:"middle",marginRight:"8px"}},o("path",{d:"M8 5v14l11-7z"})),o("span",{style:{verticalAlign:"middle"}},"播放题目"))):o("button",{class:"stop-recording-button disabled",disabled:!0},"完成回答"))())))))))}static get watchers(){return{token:["handleTokenChange"],isOpen:["handleIsOpenChange"]}}};Ve.style=Qe;const We=".input-mode-toggle{display:flex;align-items:center;margin-bottom:16px}.input-mode-toggle span{color:#333;margin-right:12px}.toggle-button{display:flex;align-items:center;background:none;border:none;color:#2E6EDF;cursor:pointer;font-size:14px;padding:4px 8px}.toggle-button svg{margin-right:4px}.free-input{width:100%}.textarea-container{margin-bottom:16px}.textarea-container label{display:block;margin-bottom:8px;font-weight:500;color:#333}.textarea-container textarea{border:1px solid #ddd;border-radius:4px;font-size:14px;resize:vertical;background-color:#f9f9f9;min-height:150px;width:100%;padding:12px 16px;margin-bottom:20px;max-width:100%;box-sizing:border-box;word-wrap:break-word}.textarea-container textarea:focus{outline:none;border-color:#2E6EDF;box-shadow:0 0 0 2px rgba(46, 110, 223, 0.2)}.required{color:#f56c6c}.input-guide{background-color:#f5f7fa;border-radius:4px;padding:12px 16px;margin-bottom:20px;max-width:100%;box-sizing:border-box;word-wrap:break-word}.guide-title{font-weight:500;color:#333;margin-bottom:8px}.guide-content{color:#666;font-size:13px;max-width:100%;word-wrap:break-word}.guide-content div{margin-bottom:4px}",Je=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",Ze=class{constructor(e){i(this,"modalTitle","劳动合同卫士");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请开始分析");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"parsedCustomInputs",{});i(this,"uploadSuccess");i(this,"streamComplete");i(this,"conversationStart");i(this,"interviewComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"filePreviewMode","window");i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",null);i(this,"showChatModal",!1);i(this,"isSubmitting",!1);i(this,"inputMode","upload");i(this,"freeInputText","");i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleFileChange",e=>{const t=e.target;t.files&&t.files.length>0&&(this.selectedFile=t.files[0])});i(this,"handleUploadClick",()=>{var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e==null||e.click()});i(this,"clearSelectedFile",()=>{var t;this.selectedFile=null,this.uploadedFileInfo=null;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e&&(e.value="")});i(this,"handleToggleInput",()=>{this.inputMode=this.inputMode==="upload"?"free":"upload"});i(this,"handleFreeInputChange",e=>{const t=e.target;this.freeInputText=t.value});i(this,"handleStartInterview",async()=>{if(this.inputMode==="upload"&&!this.selectedFile){alert("请上传合同文件");return}if(this.inputMode==="free"&&!this.freeInputText.trim()){alert("请输入合同内容");return}this.isSubmitting=!0;try{if(this.inputMode==="upload"&&!this.uploadedFileInfo&&(await this.uploadFile(),!this.uploadedFileInfo)){this.isSubmitting=!1;return}this.showChatModal=!0}catch(e){console.error("开始分析时出错:",e),x.captureError(e,{action:"handleStartInterview",component:"pcm-htws-modal",title:"开始分析时出错"}),y.emitError({error:e,message:"开始分析时出错，请重试"})}finally{this.isSubmitting=!1}});S(this,e),this.modalClosed=m(this,"modalClosed"),this.uploadSuccess=m(this,"uploadSuccess"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}handleCustomInputsChange(){this.parseCustomInputs()}parseCustomInputs(){try{typeof this.customInputs=="string"?this.parsedCustomInputs=JSON.parse(this.customInputs):this.parsedCustomInputs={...this.customInputs}}catch(e){console.error("解析 customInputs 失败:",e),this.parsedCustomInputs={},x.captureError(e,{action:"parseCustomInputs",component:"pcm-htws-modal",title:"解析自定义输入参数失败"}),y.emitError({error:e,message:"解析自定义输入参数失败"})}}componentWillLoad(){this.parseCustomInputs(),this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async uploadFile(){if(this.selectedFile){this.isUploading=!0;try{const e=await A(this.selectedFile,{},{tags:["other"]});this.uploadedFileInfo=e,this.uploadSuccess.emit(e)}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),x.captureError(e,{action:"uploadFile",component:"pcm-htws-modal",title:"文件上传失败"}),y.emitError({error:e,message:"文件上传失败，请重试"})}finally{this.isUploading=!1}}}async handleIsOpenChange(e){e?(this.parsedCustomInputs&&this.parsedCustomInputs.input&&(this.inputMode="free",this.freeInputText=this.parsedCustomInputs.input),await j(this.token),this.conversationId&&(this.showChatModal=!0)):(this.clearSelectedFile(),this.showChatModal=!1,this.freeInputText="",this.inputMode="upload")}render(){var s;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal;return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),!this.showChatModal&&!this.conversationId&&o("div",{class:"input-container"},o("div",{class:"input-mode-toggle"},o("span",null,"合同内容"),o("button",{class:"toggle-button",onClick:this.handleToggleInput},o("svg",{viewBox:"0 0 24 24",width:"16",height:"16",fill:"none",stroke:"currentColor"},o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})),"切换输入")),this.inputMode==="upload"&&o("div",{class:"resume-upload-section"},o("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?o("div",{class:"file-item"},o("div",{class:"file-item-content"},o("span",{class:"file-icon"},"📝"),o("span",{class:"file-name"},this.selectedFile.name)),o("button",{class:"remove-file",onClick:a=>{a.stopPropagation(),this.clearSelectedFile()}},"×")):o("div",{class:"upload-placeholder"},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),o("p",{class:"upload-text"},"点击上传合同"),o("p",{class:"upload-hint"},"支持markdown、pdf、docx、doc、md 格式")))),this.inputMode==="free"&&o("div",{class:"free-input"},o("div",{class:"textarea-container"},o("textarea",{id:"free-input-text",placeholder:"请输入合同内容",rows:8,value:this.freeInputText,onInput:this.handleFreeInputChange})),o("div",{class:"input-guide"},o("div",{class:"guide-title"},"输入提示："),o("div",{class:"guide-content"},o("div",null,"• 请输入完整的劳动合同内容"),o("div",null,"• 包括甲方（公司）、乙方（员工）信息"),o("div",null,"• 合同期限、工作内容、工作地点"),o("div",null,"• 工作时间、休息休假、劳动报酬"),o("div",null,"• 社会保险、劳动保护、劳动条件"),o("div",null,"• 合同变更、解除和终止条件等")))),o("button",{class:"submit-button",disabled:this.inputMode==="upload"&&!this.selectedFile||this.inputMode==="free"&&!this.freeInputText.trim()||this.isUploading||this.isSubmitting,onClick:this.handleStartInterview},this.isUploading?"上传中...":this.isSubmitting?"处理中...":"开始分析"),o("div",{class:"ai-disclaimer"},o("p",null,"所有内容均由AI生成仅供参考"),o("p",{class:"beian-info"},o("span",null,"中央网信办生成式人工智能服务备案号"),"：",o("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-************"))),o("input",{type:"file",class:"file-input",onChange:this.handleFileChange})),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,botId:"3022316191018882",conversationId:this.conversationId,defaultQuery:this.defaultQuery,enableVoice:!1,filePreviewMode:this.filePreviewMode,customInputs:this.conversationId?{}:{...this.parsedCustomInputs,file_url:this.inputMode==="upload"?(s=this.uploadedFileInfo)==null?void 0:s.cos_key:void 0,input:this.inputMode==="free"?this.freeInputText:void 0},interviewMode:"text"}))))}static get watchers(){return{token:["handleTokenChange"],customInputs:["handleCustomInputsChange"],isOpen:["handleIsOpenChange"]}}};Ze.style=We+Je;const Ge="",Ke=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",Xe=class{constructor(e){i(this,"modalTitle","会议总结助手");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请开始总结");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"uploadSuccess");i(this,"streamComplete");i(this,"conversationStart");i(this,"interviewComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"filePreviewMode","window");i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",null);i(this,"showChatModal",!1);i(this,"isSubmitting",!1);i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleFileChange",e=>{const t=e.target;t.files&&t.files.length>0&&(this.selectedFile=t.files[0])});i(this,"handleUploadClick",()=>{var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e==null||e.click()});i(this,"clearSelectedFile",()=>{var t;this.selectedFile=null,this.uploadedFileInfo=null;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e&&(e.value="")});i(this,"handleStartInterview",async()=>{if(!this.selectedFile){alert("请上传面试内容");return}this.isSubmitting=!0;try{if(!this.uploadedFileInfo&&(await this.uploadFile(),!this.uploadedFileInfo)){this.isSubmitting=!1;return}this.showChatModal=!0}catch(e){console.error("开始面试时出错:",e),x.captureError(e,{action:"handleStartInterview",component:"pcm-hyzj-modal",title:"开始面试时出错"}),y.emitError({error:e,message:"开始面试时出错，请重试"})}finally{this.isSubmitting=!1}});S(this,e),this.modalClosed=m(this,"modalClosed"),this.uploadSuccess=m(this,"uploadSuccess"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}async handleIsOpenChange(e){var t;e?(await j(this.token),(this.conversationId||(t=this.customInputs)!=null&&t.file_url)&&(this.showChatModal=!0)):(this.clearSelectedFile(),this.showChatModal=!1)}componentWillLoad(){this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async uploadFile(){if(this.selectedFile){this.isUploading=!0;try{const e=await A(this.selectedFile,{},{tags:["other"]});this.uploadedFileInfo=e,this.uploadSuccess.emit(e)}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),x.captureError(e,{action:"uploadFile",component:"pcm-hyzj-modal",title:"文件上传失败"}),y.emitError({error:e,message:"文件上传失败，请重试"})}finally{this.isUploading=!1}}}render(){var a,l,d,c;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal,s=!!(this.customInputs&&this.customInputs.file_url);return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),!this.showChatModal&&!this.conversationId&&!s&&o("div",{class:"input-container"},o("div",{class:"resume-upload-section"},o("label",null,"上传会议纪要"),o("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?o("div",{class:"file-item"},o("div",{class:"file-item-content"},o("span",{class:"file-icon"},"📝"),o("span",{class:"file-name"},this.selectedFile.name)),o("button",{class:"remove-file",onClick:p=>{p.stopPropagation(),this.clearSelectedFile()}},"×")):o("div",{class:"upload-placeholder"},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),o("p",{class:"upload-text"},"点击上传会议纪要"),o("p",{class:"upload-hint"},"支持 mp3、markdown、pdf、docx、doc、md 格式")))),o("button",{class:"submit-button",disabled:!this.selectedFile||this.isUploading||this.isSubmitting,onClick:this.handleStartInterview},this.isUploading?"上传中...":this.isSubmitting?"处理中...":"开始分析"),o("div",{class:"ai-disclaimer"},o("p",null,"所有内容均由AI生成仅供参考"),o("p",{class:"beian-info"},o("span",null,"中央网信办生成式人工智能服务备案号"),"：",o("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-************"))),o("input",{type:"file",class:"file-input",onChange:this.handleFileChange})),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,botId:"3022316191018885",conversationId:this.conversationId,defaultQuery:this.defaultQuery,enableVoice:!1,filePreviewMode:this.filePreviewMode,customInputs:this.conversationId?{}:{...this.customInputs,file_url:((a=this.customInputs)==null?void 0:a.file_url)||((l=this.uploadedFileInfo)==null?void 0:l.cos_key),file_name:((d=this.customInputs)==null?void 0:d.file_name)||((c=this.uploadedFileInfo)==null?void 0:c.file_name)},interviewMode:"text"}))))}static get watchers(){return{token:["handleTokenChange"],isOpen:["handleIsOpenChange"]}}};Xe.style=Ge+Ke;const Ye=".input-mode-toggle{display:flex;align-items:center;margin-bottom:16px}.input-mode-toggle span{color:#333;margin-right:12px}.toggle-button{display:flex;align-items:center;background:none;border:none;color:#2E6EDF;cursor:pointer;font-size:14px;padding:4px 8px}.toggle-button svg{margin-right:4px}.toggle-button:disabled{opacity:0.5;cursor:not-allowed}.structured-input{width:100%}.job-name-input{margin-bottom:20px}.job-name-input label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-name-input input{width:95%;padding:10px 12px;border:1px solid #ddd;border-radius:4px;font-size:14px;background-color:#f9f9f9}.job-name-input input:focus{outline:none;border-color:#2E6EDF;box-shadow:0 0 0 2px rgba(46, 110, 223, 0.2)}.required{color:#f56c6c}.button-container{display:flex;justify-content:center;margin-top:24px;gap:12px;max-width:100%;box-sizing:border-box;flex-wrap:wrap}.next-button{background-color:#2E6EDF;color:white}.next-button:hover{background-color:#2457b8}.next-button:disabled{background-color:#a0c0e8;cursor:not-allowed}.prev-button{background-color:#f0f0f0 !important;color:#333 !important}.prev-button:hover{background-color:#e0e0e0}.tag-selection{width:100%}.tag-selection-content{width:100%}.section-title{font-weight:600;color:#333;margin-bottom:12px;font-size:16px}.ai-tags-section,.basic-tags-section{margin-bottom:24px}.tag-group{margin-bottom:16px}.tag-title{color:#555;margin-bottom:8px;font-weight:500}.tag-container{display:flex;flex-wrap:wrap;gap:8px;max-width:100%;box-sizing:border-box}.tag{display:inline-flex;align-items:center;padding:6px 12px;border-radius:4px;background-color:#f5f5f5;border:1px solid #e0e0e0;color:#333;font-size:13px;cursor:pointer;transition:all 0.2s;max-width:100%;box-sizing:border-box;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.tag:hover{background-color:#e8e8e8}.tag-selected{background-color:#e6f0ff;border-color:#2E6EDF;color:#2E6EDF}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 0;background-color:#f9f9f9;border-radius:8px;margin:20px 0;max-width:100%;box-sizing:border-box}.loading-spinner{width:40px;height:40px;border:3px solid rgba(46, 110, 223, 0.2);border-radius:50%;border-top-color:#2E6EDF;animation:spin 1s linear infinite;margin-bottom:16px}@keyframes spin{to{transform:rotate(360deg)}}.loading-text{text-align:center;color:#333;font-weight:500}.loading-subtext{color:#888;font-size:14px;margin-top:4px}.free-input{width:100%}.textarea-container{margin-bottom:16px}.textarea-container label{display:block;margin-bottom:8px;font-weight:500;color:#333}.textarea-container textarea{border:1px solid #ddd;border-radius:4px;font-size:14px;resize:vertical;background-color:#f9f9f9;min-height:150px;width:100%;padding:12px 16px;margin-bottom:20px;max-width:100%;box-sizing:border-box;word-wrap:break-word}.textarea-container textarea:focus{outline:none;border-color:#2E6EDF;box-shadow:0 0 0 2px rgba(46, 110, 223, 0.2)}.input-guide{background-color:#f5f7fa;border-radius:4px;padding:12px 16px;margin-bottom:20px;max-width:100%;box-sizing:border-box;word-wrap:break-word}.guide-title{font-weight:500;color:#333;margin-bottom:8px}.guide-content{color:#666;font-size:13px;max-width:100%;word-wrap:break-word}.guide-content div{margin-bottom:4px}@media (max-width: 768px){.tag-container{gap:7px}.tag{padding:4px 8px;font-size:12px}}@media (max-width: 480px){.button-container{flex-direction:column;align-items:center}.next-button,.prev-button,.submit-button{width:100%;margin-bottom:8px}.tag{max-width:calc(50% - 8px)}}.tag-selection,.tag-selection-content,.ai-tags-section,.basic-tags-section,.structured-input,.free-input{max-width:100%;box-sizing:border-box}",et=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",tt=class{constructor(e){i(this,"modalTitle","职位生成");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请帮我生成职位信息");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"parsedCustomInputs",{});i(this,"streamComplete");i(this,"conversationStart");i(this,"interviewComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"filePreviewMode","window");i(this,"showChatModal",!1);i(this,"inputMode","structured");i(this,"step","input");i(this,"jobName","");i(this,"freeInputText","");i(this,"isLoading",!1);i(this,"isSubmitting",!1);i(this,"tagGroups",[]);i(this,"shuffledTagGroups",[]);i(this,"selectedAITags",{});i(this,"selectedTags",{salary:"",benefits:[],education:""});i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"salaryRanges",[{text:"3k-5k",value:"3k_5k"},{text:"5k-8k",value:"5k_8k"},{text:"8k-12k",value:"8k_12k"},{text:"12k-15k",value:"12k_15k"},{text:"15k-20k",value:"15k_20k"},{text:"20k以上",value:"above_20k"}]);i(this,"benefits",[{text:"五险一金",value:"五险一金"},{text:"年终奖",value:"年终奖"},{text:"带薪年假",value:"带薪年假"},{text:"加班补贴",value:"加班补贴"},{text:"餐补",value:"餐补"},{text:"交通补贴",value:"交通补贴"},{text:"节日福利",value:"节日福利"},{text:"团队建设",value:"团队建设"}]);i(this,"educationRequirements",[{text:"大专",value:"大专"},{text:"本科",value:"本科"},{text:"硕士",value:"硕士"},{text:"博士",value:"博士"},{text:"学历不限",value:"学历不限"}]);i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleToggleInput",()=>{this.inputMode=this.inputMode==="structured"?"free":"structured"});i(this,"handleJobNameChange",e=>{const t=e.target;this.jobName=t.value});i(this,"handleFreeInputChange",e=>{const t=e.target;this.freeInputText=t.value});i(this,"handleNextStep",async()=>{if(!this.jobName.trim()){alert("请输入职位名称");return}this.step="review",await this.handlePositionAnalysis(this.jobName)});i(this,"handlePrevStep",()=>{this.step="input"});i(this,"handleTagClick",(e,t)=>{if(e==="benefits"){const n=[...this.selectedTags.benefits],r=n.includes(t)?n.filter(s=>s!==t):[...n,t];this.selectedTags={...this.selectedTags,benefits:r}}else this.selectedTags={...this.selectedTags,[e]:this.selectedTags[e]===t?"":t}});i(this,"handleAITagClick",(e,t)=>{const n=this.selectedAITags[e]||[],r=n.includes(t)?n.filter(s=>s!==t):[...n,t];this.selectedAITags={...this.selectedAITags,[e]:r}});i(this,"handleSubmitStructured",async()=>{this.isSubmitting=!0;try{let e="";if(this.selectedTags.salary){const s=this.salaryRanges.find(a=>a.value===this.selectedTags.salary);s&&(e=s.text)}const t=this.selectedTags.benefits.join("、");let n="";if(this.selectedTags.education){const s=this.educationRequirements.find(a=>a.value===this.selectedTags.education);s&&(n=s.text)}let r=`职位名称：${this.jobName}
`;e&&(r+=`薪资范围：${e}
`),t&&(r+=`福利待遇：${t}
`),n&&(r+=`学历要求：${n}
`),Object.entries(this.selectedAITags).forEach(([s,a])=>{a.length>0&&(r+=`${s}：${a.join("、")}
`)}),this.showChatModal=!0,this.jobDescription=r}catch(e){console.error("提交结构化数据时出错:",e),x.captureError(e,{action:"handleSubmitStructured",component:"pcm-jd-modal",title:"提交数据时出错"}),y.emitError({error:e,message:"提交数据时出错，请重试"})}finally{this.isSubmitting=!1}});i(this,"handleSubmitFree",async()=>{if(!this.freeInputText.trim()){alert("请输入职位需求信息");return}this.isSubmitting=!0;try{this.jobDescription=this.freeInputText,this.showChatModal=!0}catch(e){console.error("提交自由输入数据时出错:",e),x.captureError(e,{action:"handleSubmitFree",component:"pcm-jd-modal",title:"提交数据时出错"}),y.emitError({error:e,message:"提交数据时出错，请重试"})}finally{this.isSubmitting=!1}});i(this,"jobDescription","");S(this,e),this.modalClosed=m(this,"modalClosed"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}handleCustomInputsChange(){this.parseCustomInputs()}parseCustomInputs(){try{typeof this.customInputs=="string"?this.parsedCustomInputs=JSON.parse(this.customInputs):this.parsedCustomInputs={...this.customInputs}}catch(e){console.error("解析 customInputs 失败:",e),this.parsedCustomInputs={},x.captureError(e,{action:"parseCustomInputs",component:"pcm-jd-modal",title:"解析自定义输入参数失败"}),y.emitError({error:e,message:"解析自定义输入参数失败"})}}componentWillLoad(){this.parseCustomInputs(),this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async handlePositionAnalysis(e){var t,n;if(e.trim()){this.isLoading=!0;try{const r=await M({url:"/sdk/v1/chat/workflow/block-run",method:"POST",data:{inputs:{input_info:e},workflow_code:"generate_jd_tags"}});if(r.success&&((n=(t=r.data)==null?void 0:t.data.outputs)!=null&&n.text))try{const s=JSON.parse(r.data.data.outputs.text);this.tagGroups=s.tagGroup||[];const a={},l=(s.tagGroup||[]).map(d=>{const c=[...d.defaultTags||[],...d.optionalTags||[]];for(let p=c.length-1;p>0;p--){const u=Math.floor(Math.random()*(p+1));[c[p],c[u]]=[c[u],c[p]]}return d.defaultTags&&d.defaultTags.length>0&&(a[d.dimensionName]=[...d.defaultTags]),{dimensionName:d.dimensionName,tags:c}});this.shuffledTagGroups=l,this.selectedAITags=a}catch(s){x.captureError(s,{action:"handlePositionAnalysis",component:"pcm-jd-modal",title:"解析前置标签时错误"}),y.emitError({error:s,message:"解析前置标签时错误"})}}catch(r){console.error("工作流运行错误:",r)}finally{this.isLoading=!1}}}async handleIsOpenChange(e){e?(this.parsedCustomInputs&&this.parsedCustomInputs.job_info&&(this.jobDescription=this.parsedCustomInputs.job_info,this.inputMode="free",this.freeInputText=this.parsedCustomInputs.job_info),await j(this.token),this.conversationId&&(this.showChatModal=!0)):(this.showChatModal=!1,this.jobDescription="",this.jobName="",this.freeInputText="",this.step="input",this.inputMode="structured",this.tagGroups=[],this.shuffledTagGroups=[],this.selectedAITags={},this.selectedTags={salary:"",benefits:[],education:""})}renderTagGroup(e,t,n){return o("div",{class:"tag-group"},o("div",{class:"tag-title"},e),o("div",{class:"tag-container"},t.map(r=>{const s=n==="benefits"?this.selectedTags.benefits.includes(r.value):this.selectedTags[n]===r.value;return o("div",{class:{tag:!0,"tag-selected":s},onClick:()=>this.handleTagClick(n,r.value)},r.text)})))}renderAITagGroups(){return o("div",{class:"ai-tag-groups"},this.shuffledTagGroups.map(e=>o("div",{class:"tag-group"},o("div",{class:"tag-title"},e.dimensionName),o("div",{class:"tag-container"},e.tags.map(t=>{const n=(this.selectedAITags[e.dimensionName]||[]).includes(t);return o("div",{class:{tag:!0,"tag-selected":n},onClick:()=>this.handleAITagClick(e.dimensionName,t)},t)})))))}renderLoadingState(){return o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("div",{class:"loading-text"},o("div",null,"AI 正在分析职位信息"),o("div",{class:"loading-subtext"},"请稍候...")))}render(){var s;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal;return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),!this.showChatModal&&!this.conversationId&&!0&&o("div",{class:"input-container"},o("div",{class:"input-mode-toggle"},o("span",null,"职位需求信息"),o("button",{class:"toggle-button",onClick:this.handleToggleInput,disabled:this.isLoading},o("svg",{viewBox:"0 0 24 24",width:"16",height:"16",fill:"none",stroke:"currentColor"},o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})),"切换输入")),this.inputMode==="structured"&&o("div",{class:"structured-input"},this.step==="input"&&o("div",{class:"job-name-input"},o("label",{htmlFor:"job-name"},"职位名称 ",o("span",{class:"required"},"*")),o("input",{id:"job-name",type:"text",placeholder:"请输入职位名称",value:this.jobName,onInput:this.handleJobNameChange,disabled:this.isLoading}),o("div",{class:"button-container"},o("button",{class:"submit-button next-button",onClick:this.handleNextStep,disabled:!this.jobName.trim()||this.isLoading},"下一步"))),this.step==="review"&&o("div",{class:"tag-selection"},this.isLoading?this.renderLoadingState():o("div",{class:"tag-selection-content"},this.tagGroups.length>0&&o("div",{class:"ai-tags-section"},o("div",{class:"section-title"},"AI 推荐标签"),this.renderAITagGroups()),this.tagGroups.length>0&&o("div",{class:"basic-tags-section"},this.renderTagGroup("月薪范围",this.salaryRanges,"salary"),this.renderTagGroup("福利待遇",this.benefits,"benefits"),this.renderTagGroup("学历要求",this.educationRequirements,"education")),o("div",{class:"button-container"},o("button",{class:"submit-button prev-button",onClick:this.handlePrevStep},"上一步"),o("button",{class:"submit-button",onClick:this.handleSubmitStructured,disabled:this.isSubmitting},this.isSubmitting?"处理中...":"生成JD"))))),this.inputMode==="free"&&o("div",{class:"free-input"},o("div",{class:"textarea-container"},o("label",{htmlFor:"free-input-text"},"JD信息 ",o("span",{class:"required"},"*")),o("textarea",{id:"free-input-text",placeholder:"请按照下方提示格式输入职位需求信息",rows:8,value:this.freeInputText,onInput:this.handleFreeInputChange})),o("div",{class:"input-guide"},o("div",{class:"guide-title"},"输入格式参考："),o("div",{class:"guide-content"},o("div",null,"• 职位名称 - 明确定义职位的名称"),o("div",null,"• 薪资范围 - 明确该岗位的月薪或年薪"),o("div",null,"• 福利待遇 - 该职位的福利待遇，如：五险一金、年休假、下午茶等"),o("div",null,"• 工作职责 - 描述工作内容"),o("div",null,"• 任职资格 - 包括学历、经验和技术要求"),o("div",null,"• 工作地点与性质 - 全职/兼职、远程/办公室"))),o("div",{class:"button-container"},o("button",{class:"submit-button",onClick:this.handleSubmitFree,disabled:!this.freeInputText.trim()||this.isSubmitting},this.isSubmitting?"处理中...":"生成JD"))),o("div",{class:"ai-disclaimer"},o("p",null,"所有内容均由AI生成仅供参考"),o("p",{class:"beian-info"},o("span",null,"中央网信办生成式人工智能服务备案号"),"：",o("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-************")))),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,botId:"3022316191018873",conversationId:this.conversationId,defaultQuery:this.defaultQuery,enableVoice:!1,filePreviewMode:this.filePreviewMode,customInputs:this.conversationId?{}:{...this.parsedCustomInputs,job_info:((s=this.parsedCustomInputs)==null?void 0:s.job_info)||this.jobDescription},interviewMode:"text"}))))}static get watchers(){return{token:["handleTokenChange"],customInputs:["handleCustomInputsChange"],isOpen:["handleIsOpenChange"]}}};tt.style=Ye+et;const ot="",it=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",nt=class{constructor(e){i(this,"modalTitle","简历剖析助手");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请开始分析");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"uploadSuccess");i(this,"streamComplete");i(this,"conversationStart");i(this,"interviewComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"filePreviewMode","window");i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",null);i(this,"showChatModal",!1);i(this,"jobDescription","");i(this,"isSubmitting",!1);i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleFileChange",e=>{const t=e.target;t.files&&t.files.length>0&&(this.selectedFile=t.files[0])});i(this,"handleUploadClick",()=>{var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e==null||e.click()});i(this,"clearSelectedFile",()=>{var t;this.selectedFile=null,this.uploadedFileInfo=null;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e&&(e.value="")});i(this,"handleJobDescriptionChange",e=>{const t=e.target;this.jobDescription=t.value});i(this,"handleStartAnalysis",async()=>{var e;if(!this.selectedFile){alert("请上传简历");return}if(!((e=this.customInputs)!=null&&e.job_info)&&!this.jobDescription.trim()){alert("请输入职位描述");return}this.isSubmitting=!0;try{if(!this.uploadedFileInfo&&(await this.uploadFile(),!this.uploadedFileInfo)){this.isSubmitting=!1;return}this.showChatModal=!0}catch(t){console.error("开始分析时出错:",t),x.captureError(t,{action:"handleStartAnalysis",component:"pcm-jlpp-modal",title:"开始分析时出错"}),y.emitError({error:t,message:"开始分析时出错，请重试"})}finally{this.isSubmitting=!1}});S(this,e),this.modalClosed=m(this,"modalClosed"),this.uploadSuccess=m(this,"uploadSuccess"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}async handleIsOpenChange(e){var t,n;e?(this.customInputs&&this.customInputs.job_info&&(this.jobDescription=this.customInputs.job_info),await j(this.token),(this.conversationId||(t=this.customInputs)!=null&&t.file_url&&((n=this.customInputs)!=null&&n.job_info))&&(this.showChatModal=!0)):(this.clearSelectedFile(),this.showChatModal=!1,this.jobDescription="")}componentWillLoad(){this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async uploadFile(){if(this.selectedFile){this.isUploading=!0;try{const e=await A(this.selectedFile,{},{tags:["resume"]});this.uploadedFileInfo=e,this.uploadSuccess.emit(e)}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),x.captureError(e,{action:"uploadFile",component:"pcm-jlpp-modal",title:"文件上传失败"}),y.emitError({error:e,message:"文件上传失败，请重试"})}finally{this.isUploading=!1}}}render(){var d,c,p,u,g,h,f;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal,s=!!(this.customInputs&&this.customInputs.job_info),a=!!(this.customInputs&&this.customInputs.file_url),l=!!((d=this.customInputs)!=null&&d.file_url&&((c=this.customInputs)!=null&&c.job_info));return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),!this.showChatModal&&!this.conversationId&&!l&&o("div",{class:"input-container"},!s&&o("div",{class:"jd-input-section"},o("label",{htmlFor:"job-description"},"请输入职位描述 (JD)"),o("textarea",{id:"job-description",class:"job-description-textarea",placeholder:"请输入职位描述，包括职责、要求等信息...",rows:6,value:this.jobDescription,onInput:this.handleJobDescriptionChange})),!a&&o("div",{class:"resume-upload-section"},o("label",null,"上传简历"),o("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?o("div",{class:"file-item"},o("div",{class:"file-item-content"},o("span",{class:"file-icon"},"📝"),o("span",{class:"file-name"},this.selectedFile.name)),o("button",{class:"remove-file",onClick:v=>{v.stopPropagation(),this.clearSelectedFile()}},"×")):o("div",{class:"upload-placeholder"},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),o("p",{class:"upload-text"},"点击上传简历"),o("p",{class:"upload-hint"},"支持 txt、markdown、pdf、docx、doc、md 格式")))),o("button",{class:"submit-button",disabled:!a&&!this.selectedFile||!s&&!this.jobDescription.trim()||this.isUploading||this.isSubmitting,onClick:this.handleStartAnalysis},this.isUploading?"上传中...":this.isSubmitting?"处理中...":"开始分析"),o("div",{class:"ai-disclaimer"},o("p",null,"所有内容均由AI生成仅供参考"),o("p",{class:"beian-info"},o("span",null,"中央网信办生成式人工智能服务备案号"),"：",o("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-************"))),o("input",{type:"file",class:"file-input",onChange:this.handleFileChange})),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,conversationId:this.conversationId,defaultQuery:this.defaultQuery,enableTTS:!1,enableVoice:!1,filePreviewMode:this.filePreviewMode,botId:"3022316191018881",customInputs:this.conversationId?{}:{...this.customInputs,file_url:((p=this.customInputs)==null?void 0:p.file_url)||((u=this.uploadedFileInfo)==null?void 0:u.cos_key),file_name:((g=this.customInputs)==null?void 0:g.file_name)||((h=this.uploadedFileInfo)==null?void 0:h.file_name),job_info:((f=this.customInputs)==null?void 0:f.job_info)||this.jobDescription},interviewMode:"text"}))))}static get watchers(){return{token:["handleTokenChange"],isOpen:["handleIsOpenChange"]}}};nt.style=ot+it;const rt="",st=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",at=class{constructor(e){i(this,"modalTitle","面试出题大师");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请开始出题");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"uploadSuccess");i(this,"streamComplete");i(this,"conversationStart");i(this,"interviewComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"filePreviewMode","window");i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",null);i(this,"showChatModal",!1);i(this,"jobDescription","");i(this,"isSubmitting",!1);i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleFileChange",e=>{const t=e.target;t.files&&t.files.length>0&&(this.selectedFile=t.files[0])});i(this,"handleUploadClick",()=>{var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e==null||e.click()});i(this,"clearSelectedFile",()=>{var t;this.selectedFile=null,this.uploadedFileInfo=null;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e&&(e.value="")});i(this,"handleJobDescriptionChange",e=>{const t=e.target;this.jobDescription=t.value});i(this,"handleStartInterview",async()=>{var e;if(!this.selectedFile){alert("请上传简历");return}if(!((e=this.customInputs)!=null&&e.job_info)&&!this.jobDescription.trim()){alert("请输入职位描述");return}this.isSubmitting=!0;try{if(!this.uploadedFileInfo&&(await this.uploadFile(),!this.uploadedFileInfo)){this.isSubmitting=!1;return}this.showChatModal=!0}catch(t){console.error("开始面试时出错:",t),x.captureError(t,{action:"handleStartInterview",component:"pcm-mnct-modal",title:"开始面试时出错"}),y.emitError({error:t,message:"开始面试时出错，请重试"})}finally{this.isSubmitting=!1}});S(this,e),this.modalClosed=m(this,"modalClosed"),this.uploadSuccess=m(this,"uploadSuccess"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}async handleIsOpenChange(e){var t,n;e?(this.customInputs&&this.customInputs.job_info&&(this.jobDescription=this.customInputs.job_info),await j(this.token),(this.conversationId||(t=this.customInputs)!=null&&t.file_url&&((n=this.customInputs)!=null&&n.job_info))&&(this.showChatModal=!0)):(this.clearSelectedFile(),this.showChatModal=!1,this.jobDescription="")}componentWillLoad(){this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async uploadFile(){if(this.selectedFile){this.isUploading=!0;try{const e=await A(this.selectedFile,{},{tags:["resume"]});this.uploadedFileInfo=e,this.uploadSuccess.emit(e)}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),x.captureError(e,{action:"uploadFile",component:"pcm-mnct-modal",title:"文件上传失败"}),y.emitError({error:e,message:"文件上传失败，请重试"})}finally{this.isUploading=!1}}}render(){var d,c,p,u,g,h,f;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal,s=!!(this.customInputs&&this.customInputs.job_info),a=!!(this.customInputs&&this.customInputs.file_url),l=!!((d=this.customInputs)!=null&&d.file_url&&((c=this.customInputs)!=null&&c.job_info));return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),!this.showChatModal&&!this.conversationId&&!l&&o("div",{class:"input-container"},!s&&o("div",{class:"jd-input-section"},o("label",{htmlFor:"job-description"},"请输入职位描述 (JD)"),o("textarea",{id:"job-description",class:"job-description-textarea",placeholder:"请输入职位描述，包括职责、要求等信息...",rows:6,value:this.jobDescription,onInput:this.handleJobDescriptionChange})),!a&&o("div",{class:"resume-upload-section"},o("label",null,"上传简历"),o("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?o("div",{class:"file-item"},o("div",{class:"file-item-content"},o("span",{class:"file-icon"},"📝"),o("span",{class:"file-name"},this.selectedFile.name)),o("button",{class:"remove-file",onClick:v=>{v.stopPropagation(),this.clearSelectedFile()}},"×")):o("div",{class:"upload-placeholder"},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),o("p",{class:"upload-text"},"点击上传简历"),o("p",{class:"upload-hint"},"支持 txt、markdown、pdf、docx、doc、md 格式")))),o("button",{class:"submit-button",disabled:!a&&!this.selectedFile||!s&&!this.jobDescription.trim()||this.isUploading||this.isSubmitting,onClick:this.handleStartInterview},this.isUploading?"上传中...":this.isSubmitting?"处理中...":"开始分析"),o("div",{class:"ai-disclaimer"},o("p",null,"所有内容均由AI生成仅供参考"),o("p",{class:"beian-info"},o("span",null,"中央网信办生成式人工智能服务备案号"),"：",o("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-************"))),o("input",{type:"file",class:"file-input",onChange:this.handleFileChange})),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,botId:"3022316191018876",conversationId:this.conversationId,defaultQuery:this.defaultQuery,filePreviewMode:this.filePreviewMode,enableVoice:!1,customInputs:this.conversationId?{}:{...this.customInputs,file_url:((p=this.customInputs)==null?void 0:p.file_url)||((u=this.uploadedFileInfo)==null?void 0:u.cos_key),file_name:((g=this.customInputs)==null?void 0:g.file_name)||((h=this.uploadedFileInfo)==null?void 0:h.file_name),job_info:((f=this.customInputs)==null?void 0:f.job_info)||this.jobDescription},interviewMode:"text"}))))}static get watchers(){return{token:["handleTokenChange"],isOpen:["handleIsOpenChange"]}}};at.style=rt+st;const lt="",dt=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",ct=class{constructor(e){i(this,"modalTitle","模拟面试");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请开始模拟面试");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"uploadSuccess");i(this,"streamComplete");i(this,"conversationStart");i(this,"interviewComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"filePreviewMode","window");i(this,"interviewMode","text");i(this,"recordingError");i(this,"showCopyButton",!0);i(this,"showFeedbackButtons",!0);i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",null);i(this,"showChatModal",!1);i(this,"jobDescription","");i(this,"isSubmitting",!1);i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleFileChange",e=>{const t=e.target;t.files&&t.files.length>0&&(this.selectedFile=t.files[0])});i(this,"handleUploadClick",()=>{var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e==null||e.click()});i(this,"clearSelectedFile",()=>{var t;this.selectedFile=null,this.uploadedFileInfo=null;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e&&(e.value="")});i(this,"handleJobDescriptionChange",e=>{const t=e.target;this.jobDescription=t.value});i(this,"handleStartInterview",async()=>{var e;if(!this.selectedFile){alert("请上传简历");return}if(!((e=this.customInputs)!=null&&e.job_info)&&!this.jobDescription.trim()){alert("请输入职位描述");return}this.isSubmitting=!0;try{if(!this.uploadedFileInfo&&(await this.uploadFile(),!this.uploadedFileInfo)){this.isSubmitting=!1;return}this.showChatModal=!0}catch(t){console.error("开始面试时出错:",t),x.captureError(t,{action:"handleStartInterview",component:"pcm-mnms-modal",title:"开始面试时出错"}),y.emitError({error:t,message:"开始面试时出错，请重试"})}finally{this.isSubmitting=!1}});S(this,e),this.modalClosed=m(this,"modalClosed"),this.uploadSuccess=m(this,"uploadSuccess"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent"),this.recordingError=m(this,"recordingError")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}async handleIsOpenChange(e){var t,n;e?(this.customInputs&&this.customInputs.job_info&&(this.jobDescription=this.customInputs.job_info),await j(this.token),((t=this.customInputs)!=null&&t.file_url&&((n=this.customInputs)!=null&&n.job_info)||this.conversationId)&&(this.showChatModal=!0)):(this.clearSelectedFile(),this.showChatModal=!1,this.jobDescription="")}componentWillLoad(){this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async uploadFile(){if(this.selectedFile){this.isUploading=!0;try{const e=await A(this.selectedFile,{},{tags:["resume"]});this.uploadedFileInfo=e,this.uploadSuccess.emit(e)}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),x.captureError(e,{action:"uploadFile",component:"pcm-mnms-modal",title:"文件上传失败"}),y.emitError({error:e,message:"文件上传失败，请重试"})}finally{this.isUploading=!1}}}render(){var d,c,p,u,g,h,f;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal,s=!!(this.customInputs&&this.customInputs.job_info),a=!!(this.customInputs&&this.customInputs.file_url),l=!!((d=this.customInputs)!=null&&d.file_url&&((c=this.customInputs)!=null&&c.job_info));return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),!this.showChatModal&&!this.conversationId&&!l&&o("div",{class:"input-container"},!s&&o("div",{class:"jd-input-section"},o("label",{htmlFor:"job-description"},"请输入职位描述 (JD)"),o("textarea",{id:"job-description",class:"job-description-textarea",placeholder:"请输入职位描述，包括职责、要求等信息...",rows:6,value:this.jobDescription,onInput:this.handleJobDescriptionChange})),!a&&o("div",{class:"resume-upload-section"},o("label",null,"上传简历"),o("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?o("div",{class:"file-item"},o("div",{class:"file-item-content"},o("span",{class:"file-icon"},"📝"),o("span",{class:"file-name"},this.selectedFile.name)),o("button",{class:"remove-file",onClick:v=>{v.stopPropagation(),this.clearSelectedFile()}},"×")):o("div",{class:"upload-placeholder"},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),o("p",{class:"upload-text"},"点击上传简历"),o("p",{class:"upload-hint"},"支持 txt、markdown、pdf、docx、doc、md 格式")))),o("button",{class:"submit-button",disabled:!a&&!this.selectedFile||!s&&!this.jobDescription.trim()||this.isUploading||this.isSubmitting,onClick:this.handleStartInterview},this.isUploading?"上传中...":this.isSubmitting?"处理中...":"开始分析"),o("div",{class:"ai-disclaimer"},o("p",null,"所有内容均由AI生成仅供参考"),o("p",{class:"beian-info"},o("span",null,"中央网信办生成式人工智能服务备案号"),"：",o("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-************"))),o("input",{type:"file",class:"file-input",onChange:this.handleFileChange})),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,botId:"3022316191018884",conversationId:this.conversationId,defaultQuery:this.defaultQuery,enableVoice:!1,filePreviewMode:this.filePreviewMode,showCopyButton:this.showCopyButton,showFeedbackButtons:this.showFeedbackButtons,customInputs:this.conversationId?{}:{...this.customInputs,file_url:((p=this.customInputs)==null?void 0:p.file_url)||((u=this.uploadedFileInfo)==null?void 0:u.cos_key),file_name:((g=this.customInputs)==null?void 0:g.file_name)||((h=this.uploadedFileInfo)==null?void 0:h.file_name),job_info:((f=this.customInputs)==null?void 0:f.job_info)||this.jobDescription},interviewMode:this.interviewMode}))))}static get watchers(){return{token:["handleTokenChange"],isOpen:["handleIsOpenChange"]}}};ct.style=lt+dt;const ut="",pt=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",ht=class{constructor(e){i(this,"modalTitle","模拟面试");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请开始模拟面试");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"uploadSuccess");i(this,"streamComplete");i(this,"conversationStart");i(this,"interviewComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"filePreviewMode","window");i(this,"interviewMode","text");i(this,"recordingError");i(this,"showCopyButton",!0);i(this,"showFeedbackButtons",!0);i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",null);i(this,"showChatModal",!1);i(this,"jobDescription","");i(this,"isSubmitting",!1);i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleFileChange",e=>{const t=e.target;t.files&&t.files.length>0&&(this.selectedFile=t.files[0])});i(this,"handleUploadClick",()=>{var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e==null||e.click()});i(this,"clearSelectedFile",()=>{var t;this.selectedFile=null,this.uploadedFileInfo=null;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e&&(e.value="")});i(this,"handleJobDescriptionChange",e=>{const t=e.target;this.jobDescription=t.value});i(this,"handleStartInterview",async()=>{var e;if(!this.selectedFile){alert("请上传简历");return}if(!((e=this.customInputs)!=null&&e.job_info)&&!this.jobDescription.trim()){alert("请输入职位描述");return}this.isSubmitting=!0;try{if(!this.uploadedFileInfo&&(await this.uploadFile(),!this.uploadedFileInfo)){this.isSubmitting=!1;return}this.showChatModal=!0}catch(t){console.error("开始面试时出错:",t),x.captureError(t,{action:"handleStartInterview",component:"pcm-mnms-video-modal",title:"开始面试时出错"}),y.emitError({error:t,message:"开始面试时出错，请重试"})}finally{this.isSubmitting=!1}});S(this,e),this.modalClosed=m(this,"modalClosed"),this.uploadSuccess=m(this,"uploadSuccess"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent"),this.recordingError=m(this,"recordingError")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}async handleIsOpenChange(e){var t,n;e?(this.customInputs&&this.customInputs.job_info&&(this.jobDescription=this.customInputs.job_info),await j(this.token),((t=this.customInputs)!=null&&t.file_url&&((n=this.customInputs)!=null&&n.job_info)||this.conversationId)&&(this.showChatModal=!0)):(this.clearSelectedFile(),this.showChatModal=!1,this.jobDescription="")}componentWillLoad(){this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async uploadFile(){if(this.selectedFile){this.isUploading=!0;try{const e=await A(this.selectedFile,{},{tags:["resume"]});this.uploadedFileInfo=e,this.uploadSuccess.emit(e)}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),x.captureError(e,{action:"uploadFile",component:"pcm-mnms-video-modal",title:"文件上传失败"}),y.emitError({error:e,message:"文件上传失败，请重试"})}finally{this.isUploading=!1}}}render(){var d,c,p,u,g,h,f;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal,s=!!(this.customInputs&&this.customInputs.job_info),a=!!(this.customInputs&&this.customInputs.file_url),l=!!((d=this.customInputs)!=null&&d.file_url&&((c=this.customInputs)!=null&&c.job_info));return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),!this.showChatModal&&!this.conversationId&&!l&&o("div",{class:"input-container"},!s&&o("div",{class:"jd-input-section"},o("label",{htmlFor:"job-description"},"请输入职位描述 (JD)"),o("textarea",{id:"job-description",class:"job-description-textarea",placeholder:"请输入职位描述，包括职责、要求等信息...",rows:6,value:this.jobDescription,onInput:this.handleJobDescriptionChange})),!a&&o("div",{class:"resume-upload-section"},o("label",null,"上传简历"),o("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?o("div",{class:"file-item"},o("div",{class:"file-item-content"},o("span",{class:"file-icon"},"📝"),o("span",{class:"file-name"},this.selectedFile.name)),o("button",{class:"remove-file",onClick:v=>{v.stopPropagation(),this.clearSelectedFile()}},"×")):o("div",{class:"upload-placeholder"},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),o("p",{class:"upload-text"},"点击上传简历"),o("p",{class:"upload-hint"},"支持 txt、markdown、pdf、docx、doc、md 格式")))),o("button",{class:"submit-button",disabled:!a&&!this.selectedFile||!s&&!this.jobDescription.trim()||this.isUploading||this.isSubmitting,onClick:this.handleStartInterview},this.isUploading?"上传中...":this.isSubmitting?"处理中...":"开始分析"),o("div",{class:"ai-disclaimer"},o("p",null,"所有内容均由AI生成仅供参考"),o("p",{class:"beian-info"},o("span",null,"中央网信办生成式人工智能服务备案号"),"：",o("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-************"))),o("input",{type:"file",class:"file-input",onChange:this.handleFileChange})),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,botId:"47686077653323776",conversationId:this.conversationId,defaultQuery:this.defaultQuery,enableVoice:!1,filePreviewMode:this.filePreviewMode,showCopyButton:this.showCopyButton,showFeedbackButtons:this.showFeedbackButtons,customInputs:this.conversationId?{}:{...this.customInputs,file_url:((p=this.customInputs)==null?void 0:p.file_url)||((u=this.uploadedFileInfo)==null?void 0:u.cos_key),file_name:((g=this.customInputs)==null?void 0:g.file_name)||((h=this.uploadedFileInfo)==null?void 0:h.file_name),job_info:((f=this.customInputs)==null?void 0:f.job_info)||this.jobDescription},interviewMode:this.interviewMode}))))}static get watchers(){return{token:["handleTokenChange"],isOpen:["handleIsOpenChange"]}}};ht.style=ut+pt;const mt="",gt=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",ft=class{constructor(e){i(this,"modalTitle","面试报告");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请开始分析");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"uploadSuccess");i(this,"streamComplete");i(this,"conversationStart");i(this,"interviewComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"filePreviewMode","window");i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",null);i(this,"showChatModal",!1);i(this,"jobDescription","");i(this,"isSubmitting",!1);i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleFileChange",e=>{const t=e.target;t.files&&t.files.length>0&&(this.selectedFile=t.files[0])});i(this,"handleUploadClick",()=>{var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e==null||e.click()});i(this,"clearSelectedFile",()=>{var t;this.selectedFile=null,this.uploadedFileInfo=null;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e&&(e.value="")});i(this,"handleJobDescriptionChange",e=>{const t=e.target;this.jobDescription=t.value});i(this,"handleStartInterview",async()=>{var e;if(!this.selectedFile){alert("请上传面试内容");return}if(!((e=this.customInputs)!=null&&e.job_info)&&!this.jobDescription.trim()){alert("请输入职位描述");return}this.isSubmitting=!0;try{if(!this.uploadedFileInfo&&(await this.uploadFile(),!this.uploadedFileInfo)){this.isSubmitting=!1;return}this.showChatModal=!0}catch(t){console.error("开始面试时出错:",t),x.captureError(t,{action:"handleStartInterview",component:"pcm-msbg-modal",title:"开始面试时出错"}),y.emitError({error:t,message:"开始面试时出错，请重试"})}finally{this.isSubmitting=!1}});S(this,e),this.modalClosed=m(this,"modalClosed"),this.uploadSuccess=m(this,"uploadSuccess"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}async handleIsOpenChange(e){var t,n;e?(this.customInputs&&this.customInputs.job_info&&(this.jobDescription=this.customInputs.job_info),await j(this.token),(this.conversationId||(t=this.customInputs)!=null&&t.file_urls&&((n=this.customInputs)!=null&&n.job_info))&&(this.showChatModal=!0)):(this.clearSelectedFile(),this.showChatModal=!1,this.jobDescription="")}componentWillLoad(){this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async uploadFile(){if(this.selectedFile){this.isUploading=!0;try{const e=await A(this.selectedFile,{},{tags:["other"]});this.uploadedFileInfo=e,this.uploadSuccess.emit(e)}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),x.captureError(e,{action:"uploadFile",component:"pcm-msbg-modal",title:"文件上传失败"}),y.emitError({error:e,message:"文件上传失败，请重试"})}finally{this.isUploading=!1}}}render(){var d,c,p,u,g,h,f;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal,s=!!(this.customInputs&&this.customInputs.job_info),a=!!(this.customInputs&&this.customInputs.file_urls),l=!!((d=this.customInputs)!=null&&d.file_urls&&((c=this.customInputs)!=null&&c.job_info));return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),!this.showChatModal&&!this.conversationId&&!l&&o("div",{class:"input-container"},!s&&o("div",{class:"jd-input-section"},o("label",{htmlFor:"job-description"},"请输入职位描述 (JD)"),o("textarea",{id:"job-description",class:"job-description-textarea",placeholder:"请输入职位描述，包括职责、要求等信息...",rows:6,value:this.jobDescription,onInput:this.handleJobDescriptionChange})),!a&&o("div",{class:"resume-upload-section"},o("label",null,"上传面试内容"),o("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?o("div",{class:"file-item"},o("div",{class:"file-item-content"},o("span",{class:"file-icon"},"📝"),o("span",{class:"file-name"},this.selectedFile.name)),o("button",{class:"remove-file",onClick:v=>{v.stopPropagation(),this.clearSelectedFile()}},"×")):o("div",{class:"upload-placeholder"},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),o("p",{class:"upload-text"},"点击上传面试内容"),o("p",{class:"upload-hint"},"支持 mp3、markdown、pdf、docx、doc、md 格式")))),o("button",{class:"submit-button",disabled:!a&&!this.selectedFile||!s&&!this.jobDescription.trim()||this.isUploading||this.isSubmitting,onClick:this.handleStartInterview},this.isUploading?"上传中...":this.isSubmitting?"处理中...":"开始分析"),o("div",{class:"ai-disclaimer"},o("p",null,"所有内容均由AI生成仅供参考"),o("p",{class:"beian-info"},o("span",null,"中央网信办生成式人工智能服务备案号"),"：",o("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-************"))),o("input",{type:"file",class:"file-input",onChange:this.handleFileChange})),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,botId:"3022316191018877",conversationId:this.conversationId,defaultQuery:this.defaultQuery,filePreviewMode:this.filePreviewMode,enableVoice:!1,customInputs:this.conversationId?{}:{...this.customInputs,file_urls:((p=this.customInputs)==null?void 0:p.file_urls)||((u=this.uploadedFileInfo)==null?void 0:u.cos_key),file_names:((g=this.customInputs)==null?void 0:g.file_names)||((h=this.uploadedFileInfo)==null?void 0:h.file_name),job_info:((f=this.customInputs)==null?void 0:f.job_info)||this.jobDescription},interviewMode:"text"}))))}static get watchers(){return{token:["handleTokenChange"],isOpen:["handleIsOpenChange"]}}};ft.style=mt+gt;const bt="",xt=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",wt=class{constructor(e){i(this,"modalTitle","千岗千简历");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请开始出题");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"uploadSuccess");i(this,"streamComplete");i(this,"conversationStart");i(this,"interviewComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"filePreviewMode","window");i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",null);i(this,"showChatModal",!1);i(this,"jobDescription","");i(this,"isSubmitting",!1);i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleFileChange",e=>{const t=e.target;t.files&&t.files.length>0&&(this.selectedFile=t.files[0])});i(this,"handleUploadClick",()=>{var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e==null||e.click()});i(this,"clearSelectedFile",()=>{var t;this.selectedFile=null,this.uploadedFileInfo=null;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e&&(e.value="")});i(this,"handleJobDescriptionChange",e=>{const t=e.target;this.jobDescription=t.value});i(this,"handleStartInterview",async()=>{var e;if(!this.selectedFile){alert("请上传简历");return}if(!((e=this.customInputs)!=null&&e.job_info)&&!this.jobDescription.trim()){alert("请输入职位描述");return}this.isSubmitting=!0;try{if(!this.uploadedFileInfo&&(await this.uploadFile(),!this.uploadedFileInfo)){this.isSubmitting=!1;return}this.showChatModal=!0}catch(t){console.error("开始面试时出错:",t),x.captureError(t,{action:"handleStartInterview",component:"pcm-qgqjl-modal",title:"开始面试时出错"}),y.emitError({error:t,message:"开始面试时出错，请重试"})}finally{this.isSubmitting=!1}});S(this,e),this.modalClosed=m(this,"modalClosed"),this.uploadSuccess=m(this,"uploadSuccess"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.interviewComplete=m(this,"interviewComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}async handleIsOpenChange(e){var t,n;e?(this.customInputs&&this.customInputs.job_info&&(this.jobDescription=this.customInputs.job_info),await j(this.token),(this.conversationId||(t=this.customInputs)!=null&&t.file_url&&((n=this.customInputs)!=null&&n.job_info))&&(this.showChatModal=!0)):(this.clearSelectedFile(),this.showChatModal=!1,this.jobDescription="")}componentWillLoad(){this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async uploadFile(){if(this.selectedFile){this.isUploading=!0;try{const e=await A(this.selectedFile,{},{tags:["resume"]});this.uploadedFileInfo=e,this.uploadSuccess.emit(e)}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),x.captureError(e,{action:"uploadFile",component:"pcm-qgqjl-modal",title:"文件上传失败"}),y.emitError({error:e,message:"文件上传失败，请重试"})}finally{this.isUploading=!1}}}render(){var d,c,p,u,g,h,f;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal,s=!!(this.customInputs&&this.customInputs.job_info),a=!!(this.customInputs&&this.customInputs.file_url),l=!!((d=this.customInputs)!=null&&d.file_url&&((c=this.customInputs)!=null&&c.job_info));return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),!this.showChatModal&&!this.conversationId&&!l&&o("div",{class:"input-container"},!s&&o("div",{class:"jd-input-section"},o("label",{htmlFor:"job-description"},"请输入职位描述 (JD)"),o("textarea",{id:"job-description",class:"job-description-textarea",placeholder:"请输入职位描述，包括职责、要求等信息...",rows:6,value:this.jobDescription,onInput:this.handleJobDescriptionChange})),!a&&o("div",{class:"resume-upload-section"},o("label",null,"上传简历"),o("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?o("div",{class:"file-item"},o("div",{class:"file-item-content"},o("span",{class:"file-icon"},"📝"),o("span",{class:"file-name"},this.selectedFile.name)),o("button",{class:"remove-file",onClick:v=>{v.stopPropagation(),this.clearSelectedFile()}},"×")):o("div",{class:"upload-placeholder"},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),o("p",{class:"upload-text"},"点击上传简历"),o("p",{class:"upload-hint"},"支持 txt、markdown、pdf、docx、doc、md 格式")))),o("button",{class:"submit-button",disabled:!a&&!this.selectedFile||!s&&!this.jobDescription.trim()||this.isUploading||this.isSubmitting,onClick:this.handleStartInterview},this.isUploading?"上传中...":this.isSubmitting?"处理中...":"开始分析"),o("div",{class:"ai-disclaimer"},o("p",null,"所有内容均由AI生成仅供参考"),o("p",{class:"beian-info"},o("span",null,"中央网信办生成式人工智能服务备案号"),"：",o("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-************"))),o("input",{type:"file",class:"file-input",onChange:this.handleFileChange})),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,botId:"45444431062634496",conversationId:this.conversationId,defaultQuery:this.defaultQuery,filePreviewMode:this.filePreviewMode,enableVoice:!1,customInputs:this.conversationId?{}:{...this.customInputs,file_url:((p=this.customInputs)==null?void 0:p.file_url)||((u=this.uploadedFileInfo)==null?void 0:u.cos_key),file_name:((g=this.customInputs)==null?void 0:g.file_name)||((h=this.uploadedFileInfo)==null?void 0:h.file_name),job_info:((f=this.customInputs)==null?void 0:f.job_info)||this.jobDescription},interviewMode:"text"}))))}static get watchers(){return{token:["handleTokenChange"],isOpen:["handleIsOpenChange"]}}};wt.style=bt+xt;const yt=":host{display:block;font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;overflow-y:auto;padding:20px;z-index:1000;-webkit-overflow-scrolling:touch;}.fullscreen-overlay{padding:0}.modal-container{background:white;border-radius:8px;width:100%;max-width:900px;display:flex;flex-direction:column;position:relative;margin:auto}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.modal-container.fullscreen>div:not(.modal-header):not(.initial-upload){display:flex;flex-direction:column;flex:1;overflow:hidden;height:100%}.pc-layout{width:80%;max-width:800px;min-width:320px;min-height:400px}.video-preview.placeholder{display:flex;justify-content:center;align-items:center;background:#EAEAEA}.placeholder-status{color:#00000066}.waiting-message p{margin:0;font-size:16px;color:white;font-weight:500}.recording-container{width:100%;display:flex;flex-direction:column;align-items:center}.video-area{width:100%;display:flex;flex-direction:column;align-items:center}.stop-recording-button{width:100%;height:100%;font-size:16px;background:#f44336;border-radius:6px;color:white;border:none;cursor:pointer}.stop-recording-button:hover{background:#d32f2f}.play-audio-container{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0;}.header-left{display:flex;align-items:center;gap:8px}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.chat-container{background-image:url(https://pub.pincaimao.com/static/web/images/login/bg_login_m.png);background-size:100%;height:100%;border-radius:0px 0px 8px 8px}.chat-history{position:relative;flex:1;overflow-y:auto;padding:20px;scroll-behavior:smooth;height:400px}.fullscreen .chat-history{height:auto;flex:1 1 auto}.message-input{padding:16px;border-top:1px solid #eee;display:flex;gap:8px;align-items:center}.message-input input{flex:1;padding:8px 12px;border:1px solid #ddd;border-radius:4px;outline:none;transition:border-color 0.2s ease}.message-input input:focus{border-color:#bbb}.message{margin-bottom:16px;opacity:1;transition:opacity 0.3s ease}.message-content{max-width:70%;padding:8px 12px;border-radius:8px;word-break:break-word}.message-content p{margin:0;word-break:break-word}.user-message{display:flex;justify-content:flex-end}.agent-message{display:flex;justify-content:flex-start}.user-message .message-content{background-color:#007bff;color:white}.agent-message .message-content{background-color:#f1f1f1}.message-time{font-size:12px;color:#999;margin-top:4px;display:block}.send-button{width:38px;height:38px;border-radius:16px;background:#0d75fb;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:background-color 0.2s ease}.send-button img{width:24px;height:24px}.send-button:hover{background:#0a62d6}.send-button.disabled{background:#d9d9d9;cursor:not-allowed}.empty-state{display:flex;justify-content:center;align-items:center;height:100%;color:#999;text-align:center}.loading-container{position:absolute;top:0;left:0;right:0;bottom:0;display:flex;flex-direction:column;justify-content:center;align-items:center;background-color:rgba(255, 255, 255, 0.98);z-index:1;opacity:1;transition:opacity 0.3s ease}.loading-container p{margin-top:16px;color:#666;font-size:14px}.loading-spinner{width:40px;height:40px;border:3px solid #f3f3f3;border-top:3px solid #1890ff;border-radius:50%;animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.messages-wrapper{width:100%;min-height:100%;display:flex;flex-direction:column;justify-content:flex-end}.messages-wrapper.has-overflow{justify-content:flex-start}.suggested-questions{display:flex;flex-direction:column;gap:8px;padding:16px}.suggested-question{display:flex;align-items:center;justify-content:space-between;padding:8px 12px;background-color:#f3f4f6;border-radius:4px;cursor:pointer;font-size:14px;color:#374151;transition:background-color 0.2s}.suggested-question:hover{background-color:#e5e7eb}.arrow-right{margin-left:8px}.loading-suggestions{display:flex;justify-content:center;padding:16px}.loading-spinner-small{width:20px;height:20px;border:2px solid #e5e7eb;border-top-color:#6b7280;border-radius:50%;animation:spin 1s linear infinite}.upload-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;color:#666;border-radius:4px;transition:background-color 0.2s}.upload-button:hover{background-color:rgba(0, 0, 0, 0.04)}.upload-button svg{width:20px;height:20px}.file-input{display:none}.selected-file{font-size:12px;color:#666;margin-left:8px;max-width:150px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.input-wrapper{flex:1;display:flex;align-items:center;border:1px solid #ddd;border-radius:4px;padding:0 4px;background:white}.input-wrapper input{border:none;flex:1;padding:8px;outline:none}.input-wrapper:focus-within{border-color:#bbb}.file-preview{padding:8px 16px;border-top:1px solid #eee;background-color:#f9f9f9}.recording-section{border-top:1px solid #eee;display:flex;flex-direction:column;align-items:center;padding:10px 20px 0px 20px;border-radius:14px 14px 0 0;flex:0 0 auto}.recording-section .video-preview{width:100%;height:200px;max-width:400px;position:relative;margin-bottom:10px;border:1px solid #ddd;border-radius:12px;overflow:hidden}.recording-section video{width:100%;height:100%;object-fit:cover}.recording-status{position:absolute;top:10px;left:10px;background-color:rgba(0, 0, 0, 0.6);color:white;padding:4px 8px;border-radius:4px;display:flex;align-items:center;gap:5px;font-size:14px;z-index:2}.recording-status .recording-dot{display:inline-block;width:10px;height:10px;background-color:red;border-radius:50%;margin-right:5px;animation:blink 1s infinite}.recording-status.warning{color:#ff4d4f;animation:blink 1s infinite}@keyframes blink{0%{opacity:1}50%{opacity:0.5}100%{opacity:1}}.recording-section .stop-recording-button{background-color:#f44336;color:white;border:none;cursor:pointer;font-weight:bold}.recording-section .stop-recording-button:hover{background-color:#d32f2f}.fullscreen{width:100vw;border-radius:0;height:100vh;display:flex;flex-direction:column;overflow-y:auto}.recording-controls{margin-top:10px;height:53px;width:100%;max-width:400px;display:flex;justify-content:center}.recording-controls .waiting-message{text-align:center;color:white;font-size:16px;background-image:linear-gradient(100deg, #4A9FFF 0%, #1058FF 100%);border-radius:6px;box-shadow:0 2px 8px rgba(0, 0, 0, 0.15);width:95%;display:flex;justify-content:center;align-items:center;cursor:pointer}.recording-controls .waiting-message.loading{background:#faad14}.recording-controls .waiting-message p{margin:0;font-size:16px;color:white;font-weight:500}.recording-controls .stop-recording-button{background-color:#dc3545;color:white;border:none;cursor:pointer;font-size:16px}.recording-controls .stop-recording-button:hover{background-color:#c82333}.recording-controls .stop-recording-button.disabled{background:#ccc;cursor:not-allowed}.recording-controls .stop-recording-button.disabled:hover{background:#ccc}.progress-container{display:flex;justify-content:space-between;align-items:center;width:100%;max-width:400px;margin-top:10px;padding:0 5px}.progress-bar-container{height:4px;background-color:#E5E5E5;border-radius:2px;overflow:hidden;margin-right:10px;width:75px}.progress-bar{height:100%;background-image:linear-gradient(111deg, #4A9FFF 0%, #1058FF 100%);border-radius:2px;transition:width 0.3s ease}.progress-text{font-size:14px;color:#666;white-space:nowrap}.text-input-area{display:flex;flex-direction:column;width:100%;height:100%;padding:16px;border-radius:8px;border:none;}.text-answer-input{flex:1;min-height:80px;padding:12px 12px 0px 12px;border:1px solid #ddd;border-radius:8px 8px 0 0;resize:none;font-size:16px;background-color:#fff;border-bottom:none;outline:none;}.input-toolbar{display:flex;justify-content:end;align-items:center;padding:8px 12px;background-color:#fff;border:1px solid #ddd;border-top:none;border-radius:0 0 8px 8px}.text-answer-input:focus{border-color:rgb(74, 144, 226);border-bottom:none}.text-answer-input:focus+.input-toolbar{border-color:rgb(74, 144, 226);border-top:none}.toolbar-actions{width:32px;height:32px;display:flex;justify-content:center;align-items:center;margin-right:10px;border:1px solid #d9d9d9;border-radius:6px}.toolbar-actions:hover{background-color:#f0f0f0}.toolbar-button{background:transparent;border:none;color:#666;cursor:pointer;padding:0;margin:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center}.toolbar-button>div,.toolbar-button>svg{display:flex;justify-content:center;align-items:center}.toolbar-button img{width:16px;height:16px}.submit-text-button{padding:6px 16px;background-color:#4a90e2;color:white;border:none;border-radius:4px;font-size:14px;font-weight:500;cursor:pointer;transition:background-color 0.2s}.submit-text-button:hover:not(.disabled){background-color:#3a7bc8}.submit-text-button.disabled{background-color:#b3b3b3;cursor:not-allowed}.toolbar-button.recording{background-color:rgba(255, 0, 0, 0.1);color:#ff3b30;animation:pulse 1.5s infinite}.toolbar-button.converting{background-color:rgba(0, 122, 255, 0.1);color:#007aff}.toolbar-button .recording-time{font-size:12px;margin-left:4px}.converting-indicator{display:flex;justify-content:center;align-items:center}.converting-indicator svg{animation:spin 1.5s linear infinite}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes pulse{0%{box-shadow:0 0 0 0 rgba(255, 0, 0, 0.4)}70%{box-shadow:0 0 0 6px rgba(255, 0, 0, 0)}100%{box-shadow:0 0 0 0 rgba(255, 0, 0, 0)}}@media screen and (max-width: 768px){.pc-layout{width:95%;}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}}.references-section{margin-top:30px;padding:12px;background-color:#f9f9f9;border-radius:8px;border:1px solid #e8e8e8}.references-title{font-size:14px;color:#666;margin:0 0 8px 0;font-weight:500}.references-list{display:flex;flex-direction:column;gap:8px}.reference-item{background-color:#fff;border:1px solid #e8e8e8;border-radius:6px;padding:10px;cursor:pointer;transition:background-color 0.2s, box-shadow 0.2s}.reference-item:hover{background-color:#f5f5f5;box-shadow:0 2px 8px rgba(0, 0, 0, 0.1)}.reference-header{display:flex;align-items:center;gap:6px;position:relative}.reference-icon{color:#1890ff;display:flex;align-items:center}.reference-name{font-size:13px;font-weight:500;color:#333;flex:1}.download-icon{color:#1890ff;display:flex;align-items:center}.reference-content{display:none}.suggested-questions{margin-top:20px;padding:12px;background-color:#f0f7ff;border-radius:8px;border:1px solid #d6e8ff}.suggested-title{font-size:14px;color:#1890ff;margin:0 0 8px 0;font-weight:500}.suggested-question{display:flex;align-items:center;justify-content:space-between;padding:8px 12px;background-color:#fff;border:1px solid #e6f7ff;border-radius:6px;cursor:pointer;font-size:14px;color:#1890ff;transition:all 0.3s;margin-bottom:8px}.suggested-question:last-child{margin-bottom:0}.suggested-question:hover{background-color:#e6f7ff;border-color:#91d5ff}.arrow-right{color:#1890ff;display:flex;align-items:center}.loading-suggestions{display:flex;justify-content:center;padding:16px}.loading-spinner-small{width:20px;height:20px;border:2px solid #e6f7ff;border-top-color:#1890ff;border-radius:50%;animation:spin 1s linear infinite}",vt=class{constructor(e){i(this,"modalTitle");i(this,"token");i(this,"isOpen",!1);i(this,"messages",[]);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"currentAssistantMessage","");i(this,"isLoading",!1);i(this,"currentStreamingMessage",null);i(this,"shouldAutoScroll",!0);i(this,"isLoadingHistory",!1);i(this,"streamComplete");i(this,"conversationStart");i(this,"tokenInvalid");i(this,"SCROLL_THRESHOLD",20);i(this,"fullscreen",!1);i(this,"textAnswer","");i(this,"isSubmittingText",!1);i(this,"customInputs",{show_suggested_questions:"false"});i(this,"parsedCustomInputs",{show_suggested_questions:"false"});i(this,"suggestedQuestions",[]);i(this,"suggestedQuestionsLoading",!1);i(this,"currentRefs",[]);i(this,"showReferences",!1);i(this,"employeeId");i(this,"isRecordingAudio",!1);i(this,"audioRecorder",null);i(this,"audioChunks",[]);i(this,"isConvertingAudio",!1);i(this,"audioRecordingTimeLeft",60);i(this,"audioRecordingTimer",null);i(this,"audioRecordingStartTime",0);i(this,"maxAudioRecordingTime",60);i(this,"employeeDetails",null);i(this,"isLoadingEmployeeDetails",!1);i(this,"quickQuestions",[]);i(this,"tokenInvalidListener");i(this,"clearConversation");i(this,"shouldHideReferences",!1);i(this,"isUserScrolling",!1);i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleScroll",()=>{var e;if(this.isUserScrolling){const t=(e=this.hostElement.shadowRoot)==null?void 0:e.querySelector(".chat-history");if(!t)return;const{scrollTop:n,scrollHeight:r,clientHeight:s}=t,a=r-n-s;this.shouldAutoScroll=a<=this.SCROLL_THRESHOLD}});i(this,"handleTouchStart",()=>{this.isUserScrolling=!0});i(this,"handleTouchEnd",()=>{setTimeout(()=>{this.isUserScrolling=!1},100)});i(this,"_wheelTimer",null);i(this,"handleWheel",()=>{this.isUserScrolling=!0,this._wheelTimer&&clearTimeout(this._wheelTimer),this._wheelTimer=setTimeout(()=>{this.isUserScrolling=!1},150)});i(this,"handleTextInputChange",e=>{const t=e.target;this.textAnswer=t.value});i(this,"handleKeyDown",e=>{if(e.key==="Enter"){if(e.ctrlKey)return;e.preventDefault(),this.textAnswer.trim()&&!this.isSubmittingText&&!this.isLoading&&!this.currentStreamingMessage&&this.submitTextAnswer()}});i(this,"submitTextAnswer",async()=>{if(!(!this.textAnswer.trim()||this.isSubmittingText)){this.isSubmittingText=!0;try{const e=this.textAnswer;this.textAnswer="",await this.sendMessageToAPI(e)}catch(e){console.error("提交文本回答失败:",e),alert("提交回答失败，请重试")}finally{this.isSubmittingText=!1}}});i(this,"handleVoiceInputClick",()=>{this.isRecordingAudio?this.stopAudioRecording():navigator.mediaDevices.getUserMedia({audio:!0}).then(e=>{this.startRecordingWithStream(e)}).catch(e=>{console.error("麦克风权限请求失败:",e),e.name==="NotAllowedError"||e.name==="PermissionDeniedError"?alert(`麦克风访问被拒绝。

在Mac上，请前往系统偏好设置 > 安全性与隐私 > 隐私 > 麦克风，确保您的浏览器已被允许访问麦克风。`):alert("无法访问麦克风，请确保已授予权限并且麦克风设备正常工作。")})});i(this,"handleSuggestedQuestionClick",e=>{this.isLoading||this.currentStreamingMessage||(this.textAnswer=e,this.submitTextAnswer())});i(this,"handleClearConversation",()=>{this.isLoading||this.currentStreamingMessage||(this.clearConversation.emit(this.conversationId),this.messages=[],this.currentStreamingMessage=null,this.conversationId=void 0,this.currentRefs=[],this.suggestedQuestions=[])});S(this,e),this.modalClosed=m(this,"modalClosed"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.tokenInvalid=m(this,"tokenInvalid"),this.clearConversation=m(this,"clearConversation")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}handleCustomInputsChange(){this.parseCustomInputs()}parseCustomInputs(){try{typeof this.customInputs=="string"?this.parsedCustomInputs=JSON.parse(this.customInputs):this.parsedCustomInputs={...this.customInputs}}catch(e){console.error("解析 customInputs 失败:",e),this.parsedCustomInputs={show_suggested_questions:"false"}}}componentWillLoad(){this.parseCustomInputs(),this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}async sendMessageToAPI(e){if(!this.employeeId){alert("请提供有效的数字员工ID");return}this.isLoading=!0;let t="";const n=new Date,r=`${n.getHours()}:${n.getMinutes().toString().padStart(2,"0")}`,s=e.trim()||"请开始";this.suggestedQuestions=[],this.currentRefs=[],this.shouldHideReferences=!1;const a={id:`temp-${Date.now()}`,conversation_id:this.conversationId,parent_message_id:"00000000-0000-0000-0000-000000000000",inputs:this.parsedCustomInputs||{},query:s,answer:"",message_files:[],feedback:{},retriever_resources:[],created_at:Math.floor(Date.now()/1e3).toString(),agent_thoughts:[],status:"normal",error:null,time:r,isStreaming:!0};this.currentStreamingMessage=a,this.shouldAutoScroll=!0,this.scrollToBottom();const l={response_mode:"streaming",conversation_id:this.conversationId,query:s,employee_id:this.employeeId};l.inputs={...this.parsedCustomInputs},await te({url:"/sdk/v1/knowledge/chat/chat-messages",method:"POST",data:l,onMessage:d=>{var c,p;if(d.conversation_id&&!this.conversationId&&(this.conversationId=d.conversation_id,this.conversationStart.emit({conversation_id:d.conversation_id,event:d.event,message_id:d.message_id,id:d.id})),d.event==="node_started"&&d.data.title.includes("聘才猫推荐开始")&&(this.suggestedQuestionsLoading=!0),d.event==="node_started"&&d.data.title.includes("不显示引用")&&(this.shouldHideReferences=!0),d.event==="node_finished"&&d.data.title.includes("聘才猫推荐结束")){let u=[];try{let g=d.data.outputs.text;if(typeof g=="string"){g=g.replace(/```json\n/,"").replace(/```/,"").trim();const h=JSON.parse(g);h.status==="success"&&Array.isArray(h.items)&&(u=h.items.map(f=>f.question))}else{const h=g;h.status==="success"&&Array.isArray(h.items)&&(u=h.items.map(f=>f.question))}}catch(g){console.warn("解析问题建议失败:",g)}this.suggestedQuestions=u,this.suggestedQuestionsLoading=!1}if(d.event==="node_finished"&&((p=(c=d.data)==null?void 0:c.inputs)!=null&&p.documents)){const u=[];d.data.inputs.documents.forEach(h=>{h.result_list&&Array.isArray(h.result_list)&&h.result_list.forEach(f=>{f.doc_info&&f.content&&u.push({doc_info:{doc_name:f.doc_info.doc_name,doc_id:f.doc_info.doc_id},content:f.content})})});const g=new Map;u.forEach(h=>{h.doc_info&&h.doc_info.doc_id&&g.set(h.doc_info.doc_id,h)}),this.currentRefs=Array.from(g.values())}if(d.event==="message"&&(d.event==="agent_message"||d.event==="message")&&d.answer){t+=d.answer;const u={...this.currentStreamingMessage,answer:t,id:d.message_id||this.currentStreamingMessage.id,isStreaming:!0,parent_message_id:d.parent_message_id||this.currentStreamingMessage.parent_message_id,retriever_resources:d.retriever_resources||this.currentStreamingMessage.retriever_resources,agent_thoughts:d.agent_thoughts||this.currentStreamingMessage.agent_thoughts};this.currentStreamingMessage=u,this.scrollToBottom()}d.event==="message_end"&&this.streamComplete.emit({conversation_id:d.conversation_id||"",event:d.event,message_id:d.message_id,id:d.id})},onError:d=>{console.error("发生错误:",d),alert(d instanceof Error?d.message:"消息发送失败，请稍后再试"),this.messages=[...this.messages,{...a,answer:"抱歉，发生了错误，请稍后再试。",error:d,isStreaming:!1}],this.currentStreamingMessage=null,this.isLoading=!1},onComplete:async()=>{this.isLoading=!1;const d=this.currentStreamingMessage;d.isStreaming=!1,this.messages=[...this.messages,d],this.currentStreamingMessage=null}})}scrollToBottom(){var t;if(!this.shouldAutoScroll)return;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".chat-history");e&&this.isOpen&&(e.scrollTop=e.scrollHeight)}componentDidRender(){var e;if(this.isLoadingHistory||this.shouldAutoScroll&&this.isOpen){const t=(e=this.hostElement.shadowRoot)==null?void 0:e.querySelector(".chat-history");t&&(t.scrollTop=t.scrollHeight)}}async loadHistoryMessages(){if(this.conversationId){this.isLoadingHistory=!0;try{const e=await M({url:"/sdk/v1/knowledge/chat/conversation-history",method:"GET",data:{conversation_id:this.conversationId}});if(e.success&&e.data){const n=(e.data.data||[]).map(r=>{const s=new Date(parseInt(r.created_at)*1e3),a=s.getHours().toString().padStart(2,"0"),l=s.getMinutes().toString().padStart(2,"0"),d=`${a}:${l}`;return{id:r.id,conversation_id:r.conversation_id,parent_message_id:r.parent_message_id||"00000000-0000-0000-0000-000000000000",inputs:r.inputs||{},query:r.query||"",answer:r.answer||"",message_files:r.message_files||[],feedback:r.feedback||{},retriever_resources:r.retriever_resources||[],created_at:r.created_at,agent_thoughts:r.agent_thoughts||[],status:r.status||"normal",error:r.error,time:d,isStreaming:!1}});this.messages=n}}catch(e){console.error("加载历史消息失败:",e),alert(e instanceof Error?e.message:"加载历史消息失败，请刷新重试")}finally{this.isLoadingHistory=!1,setTimeout(()=>{this.shouldAutoScroll=!0,this.scrollToBottom()},200)}}}async convertAudioToText(e){try{const t=await M({url:"/sdk/v1/tts/audio_to_text",method:"POST",data:{cos_key:e}});return t.success&&t.data&&t.data.text?t.data.text:(console.warn("音频转文字返回结果格式不正确"),null)}catch(t){return console.error("音频转文字错误:",t),null}}async fetchEmployeeDetails(){if(!(!this.employeeId||!this.token)){this.isLoadingEmployeeDetails=!0;try{const e=await M({url:`/sdk/v1/knowledge/chat/employee/${this.employeeId}`,method:"GET"});e.success&&e.data?(this.employeeDetails=e.data,this.employeeDetails.show_quote_doc!==void 0&&(this.showReferences=this.employeeDetails.show_quote_doc),this.employeeDetails.quick_questions&&(this.quickQuestions=this.employeeDetails.quick_questions.split(",").map(t=>t.trim()).filter(t=>t)),this.conversationId&&await this.loadHistoryMessages()):alert("获取智能体详情失败，请检查token和employeeId是否正确")}catch(e){console.error("获取智能体详情失败:",e)}finally{this.isLoadingEmployeeDetails=!1}}}async handleIsOpenChange(e){if(e){if(!this.employeeId){console.error("未提供数字员工ID (employeeId)"),setTimeout(()=>alert("请提供有效的数字员工ID"),0);return}await j(this.token),await this.fetchEmployeeDetails()}}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.audioRecordingTimer&&(clearInterval(this.audioRecordingTimer),this.audioRecordingTimer=null),this.stopAudioRecording()}startRecordingWithStream(e){try{const t=this.getSupportedAudioMimeType();let n;try{n=new MediaRecorder(e,{mimeType:t})}catch(r){console.warn("指定的音频MIME类型不受支持，使用默认设置:",r);try{n=new MediaRecorder(e)}catch(s){e.getTracks().forEach(a=>a.stop()),console.error("无法创建音频录制器:",s),alert("您的浏览器不支持音频录制功能");return}}this.audioRecorder=n,this.audioChunks=[],this.isRecordingAudio=!0,this.audioRecordingStartTime=Date.now(),this.audioRecordingTimeLeft=this.maxAudioRecordingTime,n.ondataavailable=r=>{r.data.size>0&&this.audioChunks.push(r.data)},n.onstop=()=>{e.getTracks().forEach(r=>r.stop()),this.processAudioRecording()},n.start(100),this.audioRecordingTimer=setInterval(()=>{const r=Math.floor((Date.now()-this.audioRecordingStartTime)/1e3);this.audioRecordingTimeLeft=Math.max(0,this.maxAudioRecordingTime-r),this.audioRecordingTimeLeft<=0&&this.stopAudioRecording()},1e3)}catch(t){e.getTracks().forEach(n=>n.stop()),console.error("开始录音失败:",t),alert("开始录音失败，请确保麦克风设备正常工作")}}async processAudioRecording(){var e;if(this.audioChunks.length===0){console.warn("没有录制到音频数据");return}try{this.isConvertingAudio=!0;const t=this.getSupportedAudioMimeType()||"audio/webm",n=new Blob(this.audioChunks,{type:t});if(n.size===0){console.warn("录制的音频为空"),this.isConvertingAudio=!1;return}const s=`audio_input.${t.includes("webm")?"webm":"mp3"}`,a=new File([n],s,{type:t}),l=await A(a,{},{tags:["audio"]}),d=await this.convertAudioToText(l.cos_key);if(d){const c=(e=this.hostElement.shadowRoot)==null?void 0:e.querySelector(".text-answer-input"),p=(c==null?void 0:c.selectionStart)||this.textAnswer.length,u=this.textAnswer.substring(0,p),g=this.textAnswer.substring(p),h=u.length>0&&!u.endsWith(" ")?" ":"";this.textAnswer=u+h+d+g,setTimeout(()=>{if(c){const f=p+h.length+d.length;c.focus(),c.setSelectionRange(f,f)}},0)}else console.warn("未能识别语音内容"),alert("未能识别语音内容，请重试或直接输入文字")}catch(t){console.error("处理音频录制失败:",t),alert("语音识别失败，请重试")}finally{this.isConvertingAudio=!1,this.audioChunks=[]}}getSupportedAudioMimeType(){const e=["audio/webm;codecs=opus","audio/webm","audio/mp4","audio/ogg;codecs=opus","audio/ogg",""];if(!window.MediaRecorder)return console.warn("MediaRecorder API不可用"),"";for(const t of e){if(!t)return"";try{if(MediaRecorder.isTypeSupported(t))return t}catch(n){console.warn(`检查音频MIME类型支持时出错 ${t}:`,n)}}return console.warn("没有找到支持的音频MIME类型，将使用浏览器默认值"),""}stopAudioRecording(){this.audioRecorder&&this.isRecordingAudio&&(this.audioRecorder.stop(),this.isRecordingAudio=!1,this.audioRecordingTimer&&(clearInterval(this.audioRecordingTimer),this.audioRecordingTimer=null))}async handleDocumentDownload(e){var t;try{const n=e.doc_info.doc_id.match(/docID_(\d+)/);if(!n||!n[1]){alert("无法解析文档ID");return}const r=n[1],s=await M({url:`/sdk/v1/files/${r}/info`,method:"GET"});if(s.success&&((t=s.data)!=null&&t.file_url)){const a=s.data.file_url,l=`${a}${a.includes("?")?"&":"?"}ci-process=doc-preview&copyable=1&dstType=html`;window.open(l,"_blank")}else alert("获取文档链接失败")}catch(n){console.error("下载文档失败:",n),alert("下载文档失败，请稍后再试")}}render(){var p,u,g;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.icon||((p=this.employeeDetails)==null?void 0:p.avatar)||"",s=this.modalTitle||((u=this.employeeDetails)==null?void 0:u.name)||"在线客服",a=()=>!this.showReferences||this.currentRefs.length===0||this.currentStreamingMessage||this.shouldHideReferences?null:o("div",{class:"references-section"},o("h3",{class:"references-title"},"引用文档"),o("div",{class:"references-list"},this.currentRefs.map((h,f)=>o("div",{class:"reference-item",key:`ref-${f}`,onClick:()=>this.handleDocumentDownload(h)},o("div",{class:"reference-header"},o("span",{class:"reference-icon"},o("svg",{viewBox:"0 0 24 24",width:"16",height:"16",fill:"currentColor"},o("path",{d:"M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zM6 20V4h7v5h5v11H6z"}))),o("span",{class:"reference-name"},h.doc_info.doc_name),o("span",{class:"download-icon"},o("svg",{viewBox:"0 0 24 24",width:"16",height:"16",fill:"currentColor"},o("path",{d:"M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"})))))))),l=()=>!this.parsedCustomInputs.show_suggested_questions&&!this.currentStreamingMessage?null:this.suggestedQuestionsLoading?o("div",{class:"loading-suggestions"},o("div",{class:"loading-spinner-small"})):this.suggestedQuestions.length===0?null:o("div",{class:"suggested-questions"},o("h3",{class:"suggested-title"},"推荐问题"),this.suggestedQuestions.map((h,f)=>o("div",{class:"suggested-question",key:`question-${f}`,onClick:()=>this.handleSuggestedQuestionClick(h)},o("span",null,h),o("span",{class:"arrow-right"},o("svg",{viewBox:"0 0 24 24",width:"16",height:"16",fill:"currentColor"},o("path",{d:"M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"})))))),d=()=>this.conversationId||this.quickQuestions.length===0||this.currentStreamingMessage?null:o("div",{class:"suggested-questions"},o("h3",{class:"suggested-title"},"常见问题"),this.quickQuestions.map((h,f)=>o("div",{class:"suggested-question",key:`preset-question-${f}`,onClick:()=>this.handleSuggestedQuestionClick(h)},o("span",null,h),o("span",{class:"arrow-right"},o("svg",{viewBox:"0 0 24 24",width:"16",height:"16",fill:"currentColor"},o("path",{d:"M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"})))))),c=()=>o("div",{class:"text-input-area"},o("textarea",{class:"text-answer-input",placeholder:"发消息",value:this.textAnswer,onInput:this.handleTextInputChange,onKeyDown:this.handleKeyDown,disabled:this.isRecordingAudio||this.isConvertingAudio}),o("div",{class:"input-toolbar"},o("div",{class:"toolbar-actions"},o("button",{class:"toolbar-button",title:"清除对话记录",onClick:this.handleClearConversation,disabled:this.isSubmittingText||this.isLoading||!!this.currentStreamingMessage||this.messages.length===0},o("img",{src:"https://pcm-pub-1351162788.cos.ap-guangzhou.myqcloud.com/sdk/image/brush-alt-svgrepo-com.png",alt:"清除对话记录"}))),o("div",{class:"toolbar-actions"},o("button",{class:{"toolbar-button":!0,recording:this.isRecordingAudio,converting:this.isConvertingAudio},title:this.isRecordingAudio?"点击停止录音":this.isConvertingAudio?"正在识别语音...":"语音输入",onClick:this.handleVoiceInputClick,disabled:this.isConvertingAudio||this.isSubmittingText||this.isLoading||!!this.currentStreamingMessage},this.isRecordingAudio?o("div",null,o("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"currentColor"},o("path",{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}),o("path",{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"}),o("circle",{cx:"12",cy:"11",r:"4",fill:"red"}))):this.isConvertingAudio?o("div",{class:"converting-indicator"},o("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"currentColor"},o("path",{d:"M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"}))):o("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"currentColor"},o("path",{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}),o("path",{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"})))),o("div",{class:{"send-button":!0,disabled:!this.textAnswer.trim()||this.isSubmittingText||this.isLoading||!!this.currentStreamingMessage||this.isRecordingAudio||this.isConvertingAudio},onClick:()=>{!this.textAnswer.trim()||this.isSubmittingText||this.isLoading||this.currentStreamingMessage||this.isRecordingAudio||this.isConvertingAudio||this.submitTextAnswer()}},o("img",{src:"https://pcm-pub-1351162788.cos.ap-guangzhou.myqcloud.com/sdk/image/i_send.png",alt:"发送"}))));return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},r&&o("img",{src:r,class:"header-icon",alt:"应用图标"}),o("div",null,s)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),o("div",{class:"chat-container"},o("div",{class:"chat-history",onScroll:this.handleScroll,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onWheel:this.handleWheel},this.isLoadingHistory?o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",null,"加载历史消息中...")):o("div",null,this.messages.map(h=>o("div",{id:`message_${h.id}`,key:h.id},o("pcm-chat-message",{message:h,showFeedbackButtons:!1,onMessageChange:f=>{const v=this.messages.map(C=>C.id===h.id?{...C,...f.detail}:C);this.messages=v}}))),this.currentStreamingMessage&&o("div",{id:`message_${this.currentStreamingMessage.id}`},o("pcm-chat-message",{message:this.currentStreamingMessage,showFeedbackButtons:!1})),this.messages.length===0&&!this.currentStreamingMessage&&o("div",{class:"empty-state"},this.isLoadingEmployeeDetails?o("p",null,"加载中..."):(g=this.employeeDetails)!=null&&g.default_greeting?o("p",null,this.employeeDetails.default_greeting):o("p",null,"请输入...")),a(),l(),d())),o("div",{class:"recording-section"},o("div",{class:"recording-container"},c())))))}static get watchers(){return{token:["handleTokenChange"],customInputs:["handleCustomInputsChange"],isOpen:["handleIsOpenChange"]}}};vt.style=yt;const kt=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",Ct=".plan-type-section{display:flex;flex-direction:column;gap:10px;margin-bottom:20px}.plan-type-section label{font-weight:600;color:#333;margin-bottom:8px}.plan-type-options{display:flex;gap:15px;flex-wrap:wrap}.plan-type-option{flex:1;min-width:120px;border:1px solid #e8e8e8;border-radius:8px;padding:15px;cursor:pointer;display:flex;flex-direction:column;align-items:center;transition:all 0.3s}.plan-type-option:hover{border-color:#1890ff;background-color:#f0f7ff}.plan-type-option.selected{border-color:#1890ff;background-color:#e6f7ff;box-shadow:0 2px 8px rgba(24, 144, 255, 0.2)}.option-icon{font-size:24px;margin-bottom:8px}.option-label{font-size:14px;font-weight:500;color:#333}.resume-upload-section{display:flex;flex-direction:column;gap:8px}.resume-upload-section label{font-weight:600;color:#333;margin-bottom:8px}",It=class{constructor(e){i(this,"modalTitle","职业规划助手");i(this,"token");i(this,"isOpen",!1);i(this,"modalClosed");i(this,"icon");i(this,"zIndex",1e3);i(this,"isShowHeader",!0);i(this,"isNeedClose",!0);i(this,"conversationId");i(this,"defaultQuery","请开始规划");i(this,"fullscreen",!1);i(this,"customInputs",{});i(this,"uploadSuccess");i(this,"streamComplete");i(this,"conversationStart");i(this,"planningComplete");i(this,"tokenInvalid");i(this,"someErrorEvent");i(this,"filePreviewMode","window");i(this,"selectedFile",null);i(this,"isUploading",!1);i(this,"uploadedFileInfo",null);i(this,"showChatModal",!1);i(this,"isSubmitting",!1);i(this,"selectedPlanType","长期规划");i(this,"tokenInvalidListener");i(this,"removeErrorListener");i(this,"handleClose",()=>{this.modalClosed.emit()});i(this,"handleFileChange",e=>{const t=e.target;t.files&&t.files.length>0&&(this.selectedFile=t.files[0])});i(this,"handleUploadClick",()=>{var t;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e==null||e.click()});i(this,"clearSelectedFile",()=>{var t;this.selectedFile=null,this.uploadedFileInfo=null;const e=(t=this.hostElement.shadowRoot)==null?void 0:t.querySelector(".file-input");e&&(e.value="")});i(this,"handlePlanTypeChange",e=>{this.selectedPlanType=e});i(this,"handleStartPlanning",async()=>{if(!this.selectedFile){alert("请上传简历");return}this.isSubmitting=!0;try{if(!this.uploadedFileInfo&&(await this.uploadFile(),!this.uploadedFileInfo)){this.isSubmitting=!1;return}this.showChatModal=!0}catch(e){console.error("开始规划时出错:",e),x.captureError(e,{action:"handleStartPlanning",component:"pcm-zygh-modal",title:"开始规划时出错"}),y.emitError({error:e,message:"开始规划时出错，请重试"})}finally{this.isSubmitting=!1}});i(this,"handlePlanningComplete",e=>{this.planningComplete.emit({...e.detail,type:this.selectedPlanType})});S(this,e),this.modalClosed=m(this,"modalClosed"),this.uploadSuccess=m(this,"uploadSuccess"),this.streamComplete=m(this,"streamComplete"),this.conversationStart=m(this,"conversationStart"),this.planningComplete=m(this,"planningComplete"),this.tokenInvalid=m(this,"tokenInvalid"),this.someErrorEvent=m(this,"someErrorEvent")}get hostElement(){return _(this)}handleTokenChange(e){e&&e!==k.getToken()&&k.setToken(e)}async handleIsOpenChange(e){var t;if(!e)this.clearSelectedFile(),this.showChatModal=!1;else{if(this.customInputs&&this.customInputs.type){const n=this.customInputs.type;(n==="长期规划"||n==="转行建议"||n==="晋升路径")&&(this.selectedPlanType=n)}await j(this.token),(this.conversationId||(t=this.customInputs)!=null&&t.file_url)&&(this.showChatModal=!0)}}componentWillLoad(){this.zIndex&&z.setItem("modal-zIndex",this.zIndex),this.token&&k.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=y.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async uploadFile(){if(this.selectedFile){this.isUploading=!0;try{const e=await A(this.selectedFile,{},{tags:["resume"]});this.uploadedFileInfo=e,this.uploadSuccess.emit(e)}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),x.captureError(e,{action:"uploadFile",component:"pcm-zygh-modal",title:"文件上传失败"}),y.emitError({error:e,message:"文件上传失败，请重试"})}finally{this.isUploading=!1}}}render(){var a,l,d,c;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},t={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},r=this.conversationId&&!this.showChatModal,s=!!(this.customInputs&&this.customInputs.file_url);return o("div",{class:n,style:e},o("div",{class:t},this.isShowHeader&&o("div",{class:"modal-header"},o("div",{class:"header-left"},this.icon&&o("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),o("div",null,this.modalTitle)),this.isNeedClose&&o("button",{class:"close-button",onClick:this.handleClose},o("span",null,"×"))),!this.showChatModal&&!this.conversationId&&!s&&o("div",{class:"input-container"},o("div",{class:"plan-type-section"},o("label",null,"选择规划类型"),o("div",{class:"plan-type-options"},o("div",{class:`plan-type-option ${this.selectedPlanType==="长期规划"?"selected":""}`,onClick:()=>this.handlePlanTypeChange("长期规划")},o("div",{class:"option-icon"},"📈"),o("div",{class:"option-label"},"长期规划")),o("div",{class:`plan-type-option ${this.selectedPlanType==="转行建议"?"selected":""}`,onClick:()=>this.handlePlanTypeChange("转行建议")},o("div",{class:"option-icon"},"🔄"),o("div",{class:"option-label"},"转行建议")),o("div",{class:`plan-type-option ${this.selectedPlanType==="晋升路径"?"selected":""}`,onClick:()=>this.handlePlanTypeChange("晋升路径")},o("div",{class:"option-icon"},"🚀"),o("div",{class:"option-label"},"晋升路径")))),o("div",{class:"resume-upload-section"},o("label",null,"上传简历"),o("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?o("div",{class:"file-item"},o("div",{class:"file-item-content"},o("span",{class:"file-icon"},"📝"),o("span",{class:"file-name"},this.selectedFile.name)),o("button",{class:"remove-file",onClick:p=>{p.stopPropagation(),this.clearSelectedFile()}},"×")):o("div",{class:"upload-placeholder"},o("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),o("p",{class:"upload-text"},"点击上传简历"),o("p",{class:"upload-hint"},"支持 txt、markdown、pdf、docx、doc、md 格式")))),o("button",{class:"submit-button",disabled:!this.selectedFile||this.isUploading||this.isSubmitting,onClick:this.handleStartPlanning},this.isUploading?"上传中...":this.isSubmitting?"处理中...":"开始规划"),o("div",{class:"ai-disclaimer"},o("p",null,"所有内容均由AI生成仅供参考"),o("p",{class:"beian-info"},o("span",null,"中央网信办生成式人工智能服务备案号"),"：",o("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-************"))),o("input",{type:"file",class:"file-input",onChange:this.handleFileChange})),r&&o("div",{class:"loading-container"},o("div",{class:"loading-spinner"}),o("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&o("div",null,o("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,botId:"3022316191018898",fullscreen:this.fullscreen,conversationId:this.conversationId,defaultQuery:this.defaultQuery,enableVoice:!1,filePreviewMode:this.filePreviewMode,customInputs:this.conversationId?{}:{...this.customInputs,file_url:((a=this.customInputs)==null?void 0:a.file_url)||((l=this.uploadedFileInfo)==null?void 0:l.cos_key),file_name:((d=this.customInputs)==null?void 0:d.file_name)||((c=this.uploadedFileInfo)==null?void 0:c.file_name),type:this.selectedPlanType},interviewMode:"text",onInterviewComplete:this.handlePlanningComplete}))))}static get watchers(){return{token:["handleTokenChange"],isOpen:["handleIsOpenChange"]}}};It.style=kt+Ct;export{we as pcm_1zhanshi_mnms_modal,De as pcm_app_chat_modal,Ae as pcm_button,je as pcm_card,qe as pcm_chat_message,Ne as pcm_drawer,Ve as pcm_hr_chat_modal,Ze as pcm_htws_modal,Xe as pcm_hyzj_modal,tt as pcm_jd_modal,nt as pcm_jlpp_modal,at as pcm_mnct_modal,ct as pcm_mnms_modal,ht as pcm_mnms_video_modal,ft as pcm_msbg_modal,wt as pcm_qgqjl_modal,vt as pcm_zsk_chat_modal,It as pcm_zygh_modal};
